# Protocol Integration Guide

This guide explains how to integrate custom backend protocols with the TUN Proxy application while maintaining SOCKS5 as a fallback.

## Architecture Overview

The TUN Proxy application now supports multiple backend protocols through a flexible protocol management system:

- **ProtocolManager**: Central manager for all protocol handlers
- **ProtocolHandler**: Trait for implementing custom protocols
- **CustomProtocolClient**: Interface for user-specific protocol implementations
- **SOCKS5 Fallback**: Automatic fallback to SOCKS5 when custom protocols fail

## Supported Protocol Types

The system supports the following protocol types:

- `socks5` - SOCKS5 proxy protocol (default fallback)
- `http` - HTTP proxy protocol
- `https` - HTTPS proxy protocol
- `shadowsocks` - Shadowsocks protocol
- `vmess` - V2Ray VMess protocol
- `trojan` - Trojan protocol
- `custom` - User-defined custom protocols

## Integrating Your Custom Protocol

### Step 1: Implement CustomProtocolClient

Create a struct that implements the `CustomProtocolClient` trait:

```rust
use async_trait::async_trait;
use crate::protocol_manager::CustomProtocolClient;
use crate::traits::ProxyNode;

pub struct MyCustomProtocol {
    // Your protocol-specific fields
    server_address: String,
    auth_token: Option<String>,
    // Add more fields as needed
}

#[async_trait]
impl CustomProtocolClient for MyCustomProtocol {
    async fn connect(&mut self, node: &ProxyNode) -> Result<()> {
        // Implement your connection logic
        // Parse node.config for custom parameters
        // Establish connection to your backend
    }
    
    async fn send_tcp(&self, remote_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>> {
        // Implement TCP data handling through your protocol
    }
    
    async fn send_udp(&self, remote_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>> {
        // Implement UDP data handling through your protocol
    }
    
    async fn test_connection(&self) -> Result<bool> {
        // Implement connectivity testing
    }
    
    async fn disconnect(&mut self) -> Result<()> {
        // Implement disconnection logic
    }
}
```

### Step 2: Register Your Protocol

Register your custom protocol with the ProtocolManager:

```rust
// Create your custom protocol handler
let mut custom_handler = CustomProtocolHandler::new("my_protocol".to_string());
custom_handler.set_custom_client(Box::new(MyCustomProtocol::new()));

// Register with the protocol manager
protocol_manager.register_handler(
    "my_protocol".to_string(),
    Box::new(custom_handler)
).await?;
```

### Step 3: Configure Proxy Nodes

Configure your proxy nodes in the configuration file to use your custom protocol:

```json
{
  "nodes": [
    {
      "id": "custom_node_1",
      "name": "My Custom Server",
      "address": "your-server.com:8080",
      "protocol": "my_protocol",
      "config": {
        "auth_token": "your_auth_token",
        "encryption_key": "your_encryption_key",
        "custom_param": "custom_value"
      }
    }
  ]
}
```

## Configuration Parameters

Your custom protocol can access configuration parameters through the `ProxyNode.config` HashMap:

```rust
fn parse_config(&mut self, node: &ProxyNode) -> Result<()> {
    // Extract standard parameters
    self.server_address = node.address.clone();
    
    // Extract custom parameters
    if let Some(token) = node.config.get("auth_token") {
        self.auth_token = Some(token.clone());
    }
    
    if let Some(key) = node.config.get("encryption_key") {
        // Handle encryption key
    }
    
    // Add more parameter parsing as needed
    Ok(())
}
```

## Error Handling and Fallback

The system automatically falls back to SOCKS5 when custom protocols fail:

1. **Primary Protocol**: Your custom protocol is tried first
2. **Automatic Fallback**: If the custom protocol fails, SOCKS5 is used automatically
3. **Error Reporting**: Detailed error messages are logged for debugging

## Testing Your Protocol

Use the provided Tauri commands to test your protocol:

```javascript
// Test protocol connectivity
const isConnected = await invoke('test_protocol_connectivity', {
    protocolName: 'my_protocol'
});

// Switch to your protocol
await invoke('switch_protocol', {
    protocolName: 'my_protocol'
});
```

## Example Implementation

See `src/custom_protocol_example.rs` for a complete example implementation that demonstrates:

- Configuration parsing
- Connection establishment
- Data handling
- Error management
- Testing functionality

## Best Practices

1. **Error Handling**: Always provide detailed error messages for debugging
2. **Configuration Validation**: Validate all configuration parameters
3. **Connection Pooling**: Implement connection pooling for better performance
4. **Encryption**: Use proper encryption for sensitive data
5. **Testing**: Implement comprehensive connectivity testing
6. **Logging**: Use appropriate log levels for debugging and monitoring

## Protocol-Specific Features

### Authentication
Implement authentication in your `connect` method:

```rust
async fn connect(&mut self, node: &ProxyNode) -> Result<()> {
    // Parse authentication parameters
    let username = node.config.get("username");
    let password = node.config.get("password");
    let token = node.config.get("auth_token");
    
    // Implement your authentication logic
    self.authenticate(username, password, token).await?;
    
    Ok(())
}
```

### Encryption
Handle encryption/decryption in your data methods:

```rust
async fn send_tcp(&self, remote_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>> {
    // Encrypt outgoing data
    let encrypted_data = self.encrypt(data)?;
    
    // Send through your protocol
    let response = self.send_raw_data(encrypted_data).await?;
    
    // Decrypt response
    let decrypted_response = self.decrypt(&response)?;
    
    Ok(decrypted_response)
}
```

### Connection Management
Implement proper connection lifecycle management:

```rust
impl MyCustomProtocol {
    async fn ensure_connected(&mut self) -> Result<()> {
        if !self.is_connected() {
            self.reconnect().await?;
        }
        Ok(())
    }
    
    async fn handle_connection_error(&mut self, error: &Error) -> Result<()> {
        // Log the error
        warn!("Connection error: {}", error);
        
        // Attempt reconnection
        self.reconnect().await?;
        
        Ok(())
    }
}
```

## Troubleshooting

### Common Issues

1. **Connection Failures**: Check network connectivity and server configuration
2. **Authentication Errors**: Verify credentials and authentication method
3. **Protocol Mismatches**: Ensure client and server use compatible protocol versions
4. **Configuration Errors**: Validate all configuration parameters

### Debugging

Enable debug logging to troubleshoot issues:

```rust
use log::{debug, info, warn, error};

// Add debug logs throughout your implementation
debug!("Connecting to server: {}", self.server_address);
info!("Authentication successful");
warn!("Retrying connection due to timeout");
error!("Failed to establish connection: {}", error);
```

## Integration Checklist

- [ ] Implement `CustomProtocolClient` trait
- [ ] Handle all configuration parameters
- [ ] Implement proper error handling
- [ ] Add comprehensive logging
- [ ] Test connectivity functionality
- [ ] Validate with real backend server
- [ ] Document protocol-specific requirements
- [ ] Test fallback to SOCKS5
