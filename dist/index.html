<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tun Proxy</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: #2d2d2d;
            border-radius: 16px;
            padding: 30px;
            width: 100%;
            max-width: 350px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid #404040;
        }

        .status-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .status-text {
            font-size: 14px;
            color: #888;
            margin-bottom: 8px;
        }

        .node-name {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }

        .connection-toggle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            transition: all 0.3s ease;
            position: relative;
        }

        .connection-toggle.disconnected {
            background: #404040;
            color: #888;
        }

        .connection-toggle.connecting {
            background: #007AFF;
            color: white;
            animation: pulse 2s infinite;
        }

        .connection-toggle.connected {
            background: #34C759;
            color: white;
        }

        .connection-toggle.error {
            background: #FF3B30;
            color: white;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .toggle-icon {
            font-size: 40px;
        }

        .toggle-text {
            position: absolute;
            bottom: -30px;
            font-size: 12px;
            font-weight: 500;
        }

        .controls-section {
            margin-bottom: 30px;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-label {
            font-size: 12px;
            color: #888;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .mode-selector {
            display: flex;
            background: #404040;
            border-radius: 8px;
            overflow: hidden;
        }

        .mode-option {
            flex: 1;
            padding: 12px;
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .mode-option.active {
            background: #007AFF;
            color: white;
        }

        .node-selector {
            width: 100%;
            padding: 12px;
            background: #404040;
            border: 1px solid #555;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            cursor: pointer;
        }

        .node-selector:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .speed-chart {
            height: 100px;
            background: #404040;
            border-radius: 8px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .speed-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        .speed-info {
            position: absolute;
            top: 8px;
            right: 8px;
            font-size: 10px;
            color: #888;
        }

        .log-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #007AFF;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .log-viewer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 33vh;
            background: #1a1a1a;
            border-top: 1px solid #404040;
            transform: translateY(100%);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .log-viewer.open {
            transform: translateY(0);
        }

        .log-header {
            padding: 12px 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #2d2d2d;
        }

        .log-title {
            font-weight: 600;
            color: #fff;
        }

        .log-close {
            background: none;
            border: none;
            color: #888;
            font-size: 18px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .log-close:hover {
            background: #404040;
            color: #fff;
        }

        .log-content {
            flex: 1;
            padding: 12px 20px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 11px;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 4px;
        }

        .log-entry.info { color: #888; }
        .log-entry.warn { color: #FF9500; }
        .log-entry.error { color: #FF3B30; }

        .log-buttons {
            display: flex;
            gap: 8px;
        }

        .log-button {
            padding: 4px 8px;
            background: #404040;
            border: none;
            border-radius: 4px;
            color: white;
            font-size: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status-section">
            <div class="status-text" id="statusText">Disconnected</div>
            <div class="node-name" id="nodeName">No node selected</div>
        </div>

        <button class="connection-toggle disconnected" id="connectionToggle">
            <span class="toggle-icon">⏻</span>
            <span class="toggle-text">Click to Connect</span>
        </button>

        <div class="controls-section">
            <div class="control-group">
                <div class="control-label">Proxy Mode</div>
                <div class="mode-selector">
                    <button class="mode-option active" data-mode="global">Global</button>
                    <button class="mode-option" data-mode="rules">Rules</button>
                    <button class="mode-option" data-mode="direct">Direct</button>
                </div>
            </div>

            <div class="control-group">
                <div class="control-label">Proxy Node</div>
                <select class="node-selector" id="nodeSelector">
                    <option value="">Loading nodes...</option>
                </select>
            </div>
        </div>

        <div class="speed-chart" id="speedChart">
            <canvas class="speed-canvas" id="speedCanvas" width="350" height="100"></canvas>
            <div class="speed-info" id="speedInfo">↑ 0 KB/s ↓ 0 KB/s</div>
        </div>

        <div class="control-group">
            <div class="control-label">SOCKS5 Testing</div>
            <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                <button class="log-button" id="testSocks5" style="flex: 1;">Test Connection</button>
                <button class="log-button" id="testHttp" style="flex: 1;">Test HTTP</button>
            </div>
            <div id="testResults" style="font-size: 11px; color: #888; min-height: 20px;"></div>
        </div>
    </div>

    <button class="log-toggle" id="logToggle">📋</button>

    <div class="log-viewer" id="logViewer">
        <div class="log-header">
            <div class="log-title">Logs</div>
            <div style="display: flex; align-items: center; gap: 8px;">
                <div class="log-buttons">
                    <button class="log-button" id="copyLogs">Copy</button>
                    <button class="log-button" id="clearLogs">Clear</button>
                </div>
                <button class="log-close" id="logClose" title="Close logs">×</button>
            </div>
        </div>
        <div class="log-content" id="logContent">
            <div class="log-entry info">Application started</div>
        </div>
    </div>

    <script>
        // Basic UI interactions
        let isConnected = false;
        let currentMode = 'global';
        let logViewerOpen = false;

        const connectionToggle = document.getElementById('connectionToggle');
        const statusText = document.getElementById('statusText');
        const nodeName = document.getElementById('nodeName');
        const modeOptions = document.querySelectorAll('.mode-option');
        const nodeSelector = document.getElementById('nodeSelector');
        const logToggle = document.getElementById('logToggle');
        const logViewer = document.getElementById('logViewer');
        const logContent = document.getElementById('logContent');
        const testSocks5 = document.getElementById('testSocks5');
        const testHttp = document.getElementById('testHttp');
        const testResults = document.getElementById('testResults');
        const logClose = document.getElementById('logClose');
        const speedCanvas = document.getElementById('speedCanvas');
        const speedCtx = speedCanvas.getContext('2d');

        // Speed chart data
        const speedData = {
            upload: new Array(60).fill(0),
            download: new Array(60).fill(0),
            maxSpeed: 1024 // Start with 1KB as minimum scale
        };

        // Connection toggle
        connectionToggle.addEventListener('click', async () => {
            if (isConnected) {
                await disconnect();
            } else {
                await connect();
            }
        });

        // Mode selector
        modeOptions.forEach(option => {
            option.addEventListener('click', async () => {
                modeOptions.forEach(o => o.classList.remove('active'));
                option.classList.add('active');
                currentMode = option.dataset.mode;

                // Disable node selector in direct mode
                nodeSelector.disabled = currentMode === 'direct';

                // Call backend to change mode
                try {
                    if (window.__TAURI__) {
                        await window.__TAURI__.core.invoke('set_proxy_mode', { mode: currentMode });
                        addLogEntry(`Proxy mode changed to: ${currentMode}`, 'info');
                    }
                } catch (error) {
                    addLogEntry(`Failed to change proxy mode: ${error}`, 'error');
                }
            });
        });

        // Node selector
        nodeSelector.addEventListener('change', async () => {
            const selectedIndex = nodeSelector.selectedIndex;
            if (selectedIndex >= 0) {
                const selectedNode = nodeSelector.options[selectedIndex].textContent;
                nodeName.textContent = selectedNode;
                addLogEntry(`Selected proxy node: ${selectedNode}`, 'info');

                // If connected, this would trigger a reconnection in a real implementation
                if (isConnected) {
                    addLogEntry('Node change will take effect on next connection', 'info');
                }
            }
        });

        // Log viewer toggle functions
        function openLogViewer() {
            logViewerOpen = true;
            logViewer.classList.add('open');
            logToggle.style.display = 'none'; // Hide toggle button when log is open
        }

        function closeLogViewer() {
            logViewerOpen = false;
            logViewer.classList.remove('open');
            logToggle.style.display = 'flex'; // Show toggle button when log is closed
        }

        // Log viewer toggle
        logToggle.addEventListener('click', openLogViewer);

        // Log viewer close button
        logClose.addEventListener('click', closeLogViewer);

        // Close log viewer with ESC key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && logViewerOpen) {
                closeLogViewer();
            }
        });

        // Log buttons
        document.getElementById('copyLogs').addEventListener('click', () => {
            navigator.clipboard.writeText(logContent.textContent);
        });

        document.getElementById('clearLogs').addEventListener('click', () => {
            logContent.innerHTML = '';
        });

        // SOCKS5 test buttons
        testSocks5.addEventListener('click', async () => {
            testSocks5.disabled = true;
            testSocks5.textContent = 'Testing...';
            testResults.textContent = 'Testing SOCKS5 connection...';

            try {
                if (window.__TAURI__) {
                    const result = await window.__TAURI__.core.invoke('test_socks5_connection');
                    if (result) {
                        testResults.textContent = '✅ SOCKS5 connection successful';
                        testResults.style.color = '#34C759';
                        addLogEntry('SOCKS5 connection test passed', 'info');
                    } else {
                        testResults.textContent = '❌ SOCKS5 connection failed';
                        testResults.style.color = '#FF3B30';
                        addLogEntry('SOCKS5 connection test failed', 'error');
                    }
                }
            } catch (error) {
                testResults.textContent = `❌ Error: ${error}`;
                testResults.style.color = '#FF3B30';
                addLogEntry(`SOCKS5 test error: ${error}`, 'error');
            } finally {
                testSocks5.disabled = false;
                testSocks5.textContent = 'Test Connection';
            }
        });

        testHttp.addEventListener('click', async () => {
            testHttp.disabled = true;
            testHttp.textContent = 'Testing...';
            testResults.textContent = 'Testing HTTP through SOCKS5...';

            try {
                if (window.__TAURI__) {
                    const response = await window.__TAURI__.core.invoke('test_http_request', {
                        host: 'httpbin.org',
                        path: '/ip'
                    });

                    if (response && response.includes('200 OK')) {
                        testResults.textContent = '✅ HTTP test successful';
                        testResults.style.color = '#34C759';
                        addLogEntry('HTTP test through SOCKS5 passed', 'info');
                    } else {
                        testResults.textContent = '⚠️ HTTP test completed with issues';
                        testResults.style.color = '#FF9500';
                        addLogEntry('HTTP test completed but response may be invalid', 'warn');
                    }
                }
            } catch (error) {
                testResults.textContent = `❌ HTTP Error: ${error}`;
                testResults.style.color = '#FF3B30';
                addLogEntry(`HTTP test error: ${error}`, 'error');
            } finally {
                testHttp.disabled = false;
                testHttp.textContent = 'Test HTTP';
            }
        });

        async function connect() {
            connectionToggle.className = 'connection-toggle connecting';
            connectionToggle.querySelector('.toggle-text').textContent = 'Connecting...';
            statusText.textContent = 'Connecting...';
            connectionToggle.disabled = true;

            try {
                if (window.__TAURI__) {
                    addLogEntry('Initiating proxy connection...', 'info');
                    await window.__TAURI__.core.invoke('connect_proxy');
                    addLogEntry('Proxy connection established', 'info');
                }

                // Update state will be handled by periodic updates
                await updateConnectionState();
            } catch (error) {
                connectionToggle.className = 'connection-toggle error';
                connectionToggle.querySelector('.toggle-text').textContent = 'Error';
                statusText.textContent = 'Connection failed';
                addLogEntry(`Connection failed: ${error}`, 'error');

                // Reset to disconnected state after error
                setTimeout(() => {
                    connectionToggle.className = 'connection-toggle disconnected';
                    connectionToggle.querySelector('.toggle-text').textContent = 'Click to Connect';
                    statusText.textContent = 'Disconnected';
                }, 3000);
            } finally {
                connectionToggle.disabled = false;
            }
        }

        async function disconnect() {
            connectionToggle.disabled = true;
            statusText.textContent = 'Disconnecting...';

            try {
                if (window.__TAURI__) {
                    addLogEntry('Disconnecting from proxy...', 'info');
                    await window.__TAURI__.core.invoke('disconnect_proxy');
                    addLogEntry('Disconnected from proxy', 'info');
                }

                // Update state will be handled by periodic updates
                await updateConnectionState();
            } catch (error) {
                addLogEntry(`Disconnect failed: ${error}`, 'error');
            } finally {
                connectionToggle.disabled = false;
            }
        }

        function addLogEntry(message, level = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry ${level}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContent.appendChild(entry);
            logContent.scrollTop = logContent.scrollHeight;
        }

        // Update connection state
        async function updateConnectionState() {
            try {
                if (window.__TAURI__) {
                    const state = await window.__TAURI__.core.invoke('get_connection_state');

                    if (state.is_connected) {
                        connectionToggle.className = 'connection-toggle connected';
                        connectionToggle.querySelector('.toggle-text').textContent = 'Connected';
                        statusText.textContent = 'Connected';

                        if (state.current_node) {
                            nodeName.textContent = state.current_node;
                        }

                        // Update traffic stats
                        const stats = state.stats;
                        const speedInfo = document.getElementById('speedInfo');
                        speedInfo.textContent = `↑ ${formatBytes(stats.upload_speed)}/s ↓ ${formatBytes(stats.download_speed)}/s`;

                        // Update speed chart with real traffic data
                        updateSpeedChart(stats.upload_speed, stats.download_speed);

                    } else {
                        connectionToggle.className = 'connection-toggle disconnected';
                        connectionToggle.querySelector('.toggle-text').textContent = 'Click to Connect';
                        statusText.textContent = 'Disconnected';
                    }

                    isConnected = state.is_connected;
                }
            } catch (error) {
                addLogEntry(`Failed to update connection state: ${error}`, 'error');
            }
        }

        // Format bytes for display
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // Update speed chart
        function updateSpeedChart(uploadSpeed, downloadSpeed) {
            // Add new data points
            speedData.upload.shift();
            speedData.upload.push(uploadSpeed);
            speedData.download.shift();
            speedData.download.push(downloadSpeed);

            // Update max speed for scaling
            const currentMax = Math.max(...speedData.upload, ...speedData.download);
            if (currentMax > speedData.maxSpeed) {
                speedData.maxSpeed = currentMax * 1.2; // Add 20% padding
            } else if (currentMax < speedData.maxSpeed * 0.5) {
                speedData.maxSpeed = Math.max(currentMax * 2, 1024); // Scale down but keep minimum
            }

            drawSpeedChart();
        }

        // Draw speed chart
        function drawSpeedChart() {
            const width = speedCanvas.width;
            const height = speedCanvas.height;

            // Clear canvas
            speedCtx.clearRect(0, 0, width, height);

            // Draw background grid
            speedCtx.strokeStyle = '#555';
            speedCtx.lineWidth = 1;
            speedCtx.setLineDash([2, 2]);

            // Horizontal grid lines
            for (let i = 0; i <= 4; i++) {
                const y = (height / 4) * i;
                speedCtx.beginPath();
                speedCtx.moveTo(0, y);
                speedCtx.lineTo(width, y);
                speedCtx.stroke();
            }

            // Vertical grid lines
            for (let i = 0; i <= 6; i++) {
                const x = (width / 6) * i;
                speedCtx.beginPath();
                speedCtx.moveTo(x, 0);
                speedCtx.lineTo(x, height);
                speedCtx.stroke();
            }

            speedCtx.setLineDash([]);

            // Draw upload line (orange)
            speedCtx.strokeStyle = '#FF9500';
            speedCtx.lineWidth = 2;
            speedCtx.beginPath();
            for (let i = 0; i < speedData.upload.length; i++) {
                const x = (width / (speedData.upload.length - 1)) * i;
                const y = height - (speedData.upload[i] / speedData.maxSpeed) * height;
                if (i === 0) {
                    speedCtx.moveTo(x, y);
                } else {
                    speedCtx.lineTo(x, y);
                }
            }
            speedCtx.stroke();

            // Draw download line (blue)
            speedCtx.strokeStyle = '#007AFF';
            speedCtx.lineWidth = 2;
            speedCtx.beginPath();
            for (let i = 0; i < speedData.download.length; i++) {
                const x = (width / (speedData.download.length - 1)) * i;
                const y = height - (speedData.download[i] / speedData.maxSpeed) * height;
                if (i === 0) {
                    speedCtx.moveTo(x, y);
                } else {
                    speedCtx.lineTo(x, y);
                }
            }
            speedCtx.stroke();
        }

        // Initialize
        async function init() {
            try {
                if (window.__TAURI__) {
                    // Load available nodes
                    addLogEntry('Loading proxy nodes...', 'info');
                    const nodes = await window.__TAURI__.core.invoke('get_proxy_nodes');
                    nodeSelector.innerHTML = '';

                    if (nodes.length > 0) {
                        nodes.forEach((node, index) => {
                            const option = document.createElement('option');
                            option.value = index;
                            option.textContent = node;
                            nodeSelector.appendChild(option);
                        });

                        nodeSelector.selectedIndex = 0;
                        nodeName.textContent = nodes[0];
                        addLogEntry(`Loaded ${nodes.length} proxy nodes`, 'info');
                    } else {
                        nodeSelector.innerHTML = '<option value="">No nodes available</option>';
                        addLogEntry('No proxy nodes available', 'warn');
                    }

                    // Update initial connection state
                    await updateConnectionState();

                    // Initialize speed chart
                    drawSpeedChart();

                    // Start periodic state updates
                    setInterval(updateConnectionState, 2000);
                }
            } catch (error) {
                addLogEntry(`Failed to load nodes: ${error}`, 'error');
                nodeSelector.innerHTML = '<option value="">No nodes available</option>';
            }
        }

        // Start initialization when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
