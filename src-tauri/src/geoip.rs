use anyhow::Result;
use std::net::{IpAddr, Ipv4Addr};
use std::collections::HashMap;
use tokio::sync::RwLock;
use log::{info, debug};
use serde::{Deserialize, Serialize};

/// GeoIP database entry
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct GeoIpEntry {
    pub country_code: String,
    pub country_name: String,
    pub continent: String,
    pub city: Option<String>,
    pub latitude: Option<f64>,
    pub longitude: Option<f64>,
}

/// IP range for GeoIP lookup
#[derive(Debug, Clone)]
pub struct IpRange {
    pub start: IpAddr,
    pub end: IpAddr,
    pub entry: GeoIpEntry,
}

/// GeoSite category entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeoSiteEntry {
    pub category: String,
    pub domains: Vec<String>,
    pub keywords: Vec<String>,
    pub suffixes: Vec<String>,
}

/// Enhanced GeoIP and GeoSite manager
pub struct GeoManager {
    /// IPv4 ranges for GeoIP lookup
    ipv4_ranges: RwLock<Vec<IpRange>>,
    /// IPv6 ranges for GeoIP lookup
    ipv6_ranges: RwLock<Vec<IpRange>>,
    /// GeoIP cache for performance
    geoip_cache: RwLock<HashMap<IpAddr, GeoIpEntry>>,
    /// GeoSite categories
    geosite_categories: RwLock<HashMap<String, GeoSiteEntry>>,
    /// Domain cache for GeoSite lookups
    domain_cache: RwLock<HashMap<String, String>>,
    /// Cache size limits
    max_cache_size: usize,
}

impl GeoManager {
    pub fn new() -> Self {
        Self {
            ipv4_ranges: RwLock::new(Vec::new()),
            ipv6_ranges: RwLock::new(Vec::new()),
            geoip_cache: RwLock::new(HashMap::new()),
            geosite_categories: RwLock::new(HashMap::new()),
            domain_cache: RwLock::new(HashMap::new()),
            max_cache_size: 10000,
        }
    }

    /// Initialize with built-in data
    pub async fn initialize(&self) -> Result<()> {
        info!("Initializing GeoIP and GeoSite databases");

        // Load built-in GeoIP data
        self.load_builtin_geoip_data().await?;

        // Load built-in GeoSite data
        self.load_builtin_geosite_data().await?;

        info!("GeoIP and GeoSite databases initialized successfully");
        Ok(())
    }

    /// Load built-in GeoIP data (simplified for demonstration)
    async fn load_builtin_geoip_data(&self) -> Result<()> {
        let mut ipv4_ranges = self.ipv4_ranges.write().await;

        // Add some common IP ranges (in production, this would be loaded from MaxMind or similar)
        
        // Google DNS
        ipv4_ranges.push(IpRange {
            start: IpAddr::V4(Ipv4Addr::new(8, 8, 8, 0)),
            end: IpAddr::V4(Ipv4Addr::new(8, 8, 8, 255)),
            entry: GeoIpEntry {
                country_code: "US".to_string(),
                country_name: "United States".to_string(),
                continent: "North America".to_string(),
                city: Some("Mountain View".to_string()),
                latitude: Some(37.4056),
                longitude: Some(-122.0775),
            },
        });

        // Cloudflare DNS
        ipv4_ranges.push(IpRange {
            start: IpAddr::V4(Ipv4Addr::new(1, 1, 1, 0)),
            end: IpAddr::V4(Ipv4Addr::new(1, 1, 1, 255)),
            entry: GeoIpEntry {
                country_code: "US".to_string(),
                country_name: "United States".to_string(),
                continent: "North America".to_string(),
                city: Some("San Francisco".to_string()),
                latitude: Some(37.7749),
                longitude: Some(-122.4194),
            },
        });

        // China DNS ranges
        ipv4_ranges.push(IpRange {
            start: IpAddr::V4(Ipv4Addr::new(114, 114, 114, 0)),
            end: IpAddr::V4(Ipv4Addr::new(114, 114, 114, 255)),
            entry: GeoIpEntry {
                country_code: "CN".to_string(),
                country_name: "China".to_string(),
                continent: "Asia".to_string(),
                city: Some("Beijing".to_string()),
                latitude: Some(39.9042),
                longitude: Some(116.4074),
            },
        });

        // Alibaba DNS
        ipv4_ranges.push(IpRange {
            start: IpAddr::V4(Ipv4Addr::new(223, 5, 5, 0)),
            end: IpAddr::V4(Ipv4Addr::new(223, 5, 5, 255)),
            entry: GeoIpEntry {
                country_code: "CN".to_string(),
                country_name: "China".to_string(),
                continent: "Asia".to_string(),
                city: Some("Hangzhou".to_string()),
                latitude: Some(30.2741),
                longitude: Some(120.1551),
            },
        });

        // Private IP ranges (RFC 1918)
        ipv4_ranges.push(IpRange {
            start: IpAddr::V4(Ipv4Addr::new(192, 168, 0, 0)),
            end: IpAddr::V4(Ipv4Addr::new(192, 168, 255, 255)),
            entry: GeoIpEntry {
                country_code: "PRIVATE".to_string(),
                country_name: "Private Network".to_string(),
                continent: "Local".to_string(),
                city: None,
                latitude: None,
                longitude: None,
            },
        });

        ipv4_ranges.push(IpRange {
            start: IpAddr::V4(Ipv4Addr::new(10, 0, 0, 0)),
            end: IpAddr::V4(Ipv4Addr::new(10, 255, 255, 255)),
            entry: GeoIpEntry {
                country_code: "PRIVATE".to_string(),
                country_name: "Private Network".to_string(),
                continent: "Local".to_string(),
                city: None,
                latitude: None,
                longitude: None,
            },
        });

        ipv4_ranges.push(IpRange {
            start: IpAddr::V4(Ipv4Addr::new(172, 16, 0, 0)),
            end: IpAddr::V4(Ipv4Addr::new(172, 31, 255, 255)),
            entry: GeoIpEntry {
                country_code: "PRIVATE".to_string(),
                country_name: "Private Network".to_string(),
                continent: "Local".to_string(),
                city: None,
                latitude: None,
                longitude: None,
            },
        });

        // Sort ranges for efficient binary search
        ipv4_ranges.sort_by(|a, b| a.start.cmp(&b.start));

        info!("Loaded {} IPv4 GeoIP ranges", ipv4_ranges.len());
        Ok(())
    }

    /// Load built-in GeoSite data
    async fn load_builtin_geosite_data(&self) -> Result<()> {
        let mut categories = self.geosite_categories.write().await;

        // Google services
        categories.insert("google".to_string(), GeoSiteEntry {
            category: "google".to_string(),
            domains: vec![
                "google.com".to_string(),
                "googleapis.com".to_string(),
                "googleusercontent.com".to_string(),
                "googlevideo.com".to_string(),
                "gstatic.com".to_string(),
                "youtube.com".to_string(),
                "ytimg.com".to_string(),
                "gmail.com".to_string(),
                "googlemail.com".to_string(),
            ],
            keywords: vec![
                "google".to_string(),
                "youtube".to_string(),
                "gmail".to_string(),
            ],
            suffixes: vec![
                "google.com".to_string(),
                "googleapis.com".to_string(),
                "youtube.com".to_string(),
            ],
        });

        // Microsoft services
        categories.insert("microsoft".to_string(), GeoSiteEntry {
            category: "microsoft".to_string(),
            domains: vec![
                "microsoft.com".to_string(),
                "office.com".to_string(),
                "outlook.com".to_string(),
                "live.com".to_string(),
                "xbox.com".to_string(),
                "msn.com".to_string(),
                "bing.com".to_string(),
            ],
            keywords: vec![
                "microsoft".to_string(),
                "office".to_string(),
                "outlook".to_string(),
            ],
            suffixes: vec![
                "microsoft.com".to_string(),
                "office.com".to_string(),
                "live.com".to_string(),
            ],
        });

        // Apple services
        categories.insert("apple".to_string(), GeoSiteEntry {
            category: "apple".to_string(),
            domains: vec![
                "apple.com".to_string(),
                "icloud.com".to_string(),
                "itunes.com".to_string(),
                "appstore.com".to_string(),
                "me.com".to_string(),
                "mac.com".to_string(),
            ],
            keywords: vec![
                "apple".to_string(),
                "icloud".to_string(),
                "itunes".to_string(),
            ],
            suffixes: vec![
                "apple.com".to_string(),
                "icloud.com".to_string(),
            ],
        });

        // Social media
        categories.insert("social".to_string(), GeoSiteEntry {
            category: "social".to_string(),
            domains: vec![
                "facebook.com".to_string(),
                "twitter.com".to_string(),
                "instagram.com".to_string(),
                "linkedin.com".to_string(),
                "tiktok.com".to_string(),
                "snapchat.com".to_string(),
                "reddit.com".to_string(),
            ],
            keywords: vec![
                "facebook".to_string(),
                "twitter".to_string(),
                "instagram".to_string(),
            ],
            suffixes: vec![
                "facebook.com".to_string(),
                "twitter.com".to_string(),
                "instagram.com".to_string(),
            ],
        });

        // Streaming services
        categories.insert("streaming".to_string(), GeoSiteEntry {
            category: "streaming".to_string(),
            domains: vec![
                "netflix.com".to_string(),
                "hulu.com".to_string(),
                "disney.com".to_string(),
                "disneyplus.com".to_string(),
                "amazon.com".to_string(),
                "primevideo.com".to_string(),
                "spotify.com".to_string(),
            ],
            keywords: vec![
                "netflix".to_string(),
                "disney".to_string(),
                "spotify".to_string(),
            ],
            suffixes: vec![
                "netflix.com".to_string(),
                "disneyplus.com".to_string(),
                "spotify.com".to_string(),
            ],
        });

        // Chinese services
        categories.insert("cn".to_string(), GeoSiteEntry {
            category: "cn".to_string(),
            domains: vec![
                "baidu.com".to_string(),
                "qq.com".to_string(),
                "weibo.com".to_string(),
                "taobao.com".to_string(),
                "tmall.com".to_string(),
                "alipay.com".to_string(),
                "wechat.com".to_string(),
                "douyin.com".to_string(),
            ],
            keywords: vec![
                "baidu".to_string(),
                "tencent".to_string(),
                "alibaba".to_string(),
            ],
            suffixes: vec![
                "baidu.com".to_string(),
                "qq.com".to_string(),
                "taobao.com".to_string(),
            ],
        });

        info!("Loaded {} GeoSite categories", categories.len());
        Ok(())
    }

    /// Get country information for an IP address
    pub async fn get_country_for_ip(&self, ip: IpAddr) -> Result<Option<GeoIpEntry>> {
        // Check cache first
        {
            let cache = self.geoip_cache.read().await;
            if let Some(entry) = cache.get(&ip) {
                debug!("GeoIP cache hit for {}: {}", ip, entry.country_code);
                return Ok(Some(entry.clone()));
            }
        }

        // Search in IP ranges
        let entry = match ip {
            IpAddr::V4(_) => self.search_ipv4_ranges(ip).await?,
            IpAddr::V6(_) => self.search_ipv6_ranges(ip).await?,
        };

        // Cache the result if found
        if let Some(ref geo_entry) = entry {
            let mut cache = self.geoip_cache.write().await;

            // Limit cache size
            if cache.len() >= self.max_cache_size {
                // Remove oldest entries (simple FIFO, could be improved with LRU)
                let keys_to_remove: Vec<IpAddr> = cache.keys().take(1000).cloned().collect();
                for key in keys_to_remove {
                    cache.remove(&key);
                }
            }

            cache.insert(ip, geo_entry.clone());
            debug!("Cached GeoIP result for {}: {}", ip, geo_entry.country_code);
        }

        Ok(entry)
    }

    /// Search IPv4 ranges for IP
    async fn search_ipv4_ranges(&self, ip: IpAddr) -> Result<Option<GeoIpEntry>> {
        let ranges = self.ipv4_ranges.read().await;

        for range in ranges.iter() {
            if self.ip_in_range(ip, range.start, range.end) {
                debug!("Found IPv4 range match for {}: {}", ip, range.entry.country_code);
                return Ok(Some(range.entry.clone()));
            }
        }

        debug!("No IPv4 range match found for {}", ip);
        Ok(None)
    }

    /// Search IPv6 ranges for IP
    async fn search_ipv6_ranges(&self, ip: IpAddr) -> Result<Option<GeoIpEntry>> {
        let ranges = self.ipv6_ranges.read().await;

        for range in ranges.iter() {
            if self.ip_in_range(ip, range.start, range.end) {
                debug!("Found IPv6 range match for {}: {}", ip, range.entry.country_code);
                return Ok(Some(range.entry.clone()));
            }
        }

        debug!("No IPv6 range match found for {}", ip);
        Ok(None)
    }

    /// Check if IP is in range
    fn ip_in_range(&self, ip: IpAddr, start: IpAddr, end: IpAddr) -> bool {
        match (ip, start, end) {
            (IpAddr::V4(ip), IpAddr::V4(start), IpAddr::V4(end)) => {
                let ip_u32 = u32::from(ip);
                let start_u32 = u32::from(start);
                let end_u32 = u32::from(end);
                ip_u32 >= start_u32 && ip_u32 <= end_u32
            }
            (IpAddr::V6(ip), IpAddr::V6(start), IpAddr::V6(end)) => {
                let ip_u128 = u128::from(ip);
                let start_u128 = u128::from(start);
                let end_u128 = u128::from(end);
                ip_u128 >= start_u128 && ip_u128 <= end_u128
            }
            _ => false, // Mismatched IP versions
        }
    }

    /// Check if domain belongs to a GeoSite category
    pub async fn match_geosite(&self, domain: &str, category: &str) -> Result<bool> {
        // Check cache first
        {
            let cache = self.domain_cache.read().await;
            let cache_key = format!("{}:{}", domain, category);
            if let Some(result) = cache.get(&cache_key) {
                debug!("GeoSite cache hit for {}:{}: {}", domain, category, result);
                return Ok(result == "true");
            }
        }

        let categories = self.geosite_categories.read().await;

        if let Some(site_entry) = categories.get(category) {
            let matches = self.domain_matches_entry(domain, site_entry);

            // Cache the result
            {
                let mut cache = self.domain_cache.write().await;

                // Limit cache size
                if cache.len() >= self.max_cache_size {
                    let keys_to_remove: Vec<String> = cache.keys().take(1000).cloned().collect();
                    for key in keys_to_remove {
                        cache.remove(&key);
                    }
                }

                let cache_key = format!("{}:{}", domain, category);
                cache.insert(cache_key, if matches { "true" } else { "false" }.to_string());
            }

            debug!("GeoSite match for {}:{}: {}", domain, category, matches);
            Ok(matches)
        } else {
            debug!("GeoSite category not found: {}", category);
            Ok(false)
        }
    }

    /// Check if domain matches a GeoSite entry
    fn domain_matches_entry(&self, domain: &str, entry: &GeoSiteEntry) -> bool {
        // Check exact domain matches
        for exact_domain in &entry.domains {
            if domain == exact_domain {
                return true;
            }
        }

        // Check suffix matches
        for suffix in &entry.suffixes {
            if self.match_domain_suffix(domain, suffix) {
                return true;
            }
        }

        // Check keyword matches
        for keyword in &entry.keywords {
            if domain.contains(keyword) {
                return true;
            }
        }

        false
    }

    /// Check if domain matches a domain suffix
    fn match_domain_suffix(&self, domain: &str, suffix: &str) -> bool {
        domain.ends_with(suffix) && (domain == suffix || domain.ends_with(&format!(".{}", suffix)))
    }

    /// Get all available GeoSite categories
    pub async fn get_geosite_categories(&self) -> Result<Vec<String>> {
        let categories = self.geosite_categories.read().await;
        Ok(categories.keys().cloned().collect())
    }

    /// Get details for a specific GeoSite category
    pub async fn get_geosite_category_details(&self, category: &str) -> Result<Option<GeoSiteEntry>> {
        let categories = self.geosite_categories.read().await;
        Ok(categories.get(category).cloned())
    }

    /// Add custom GeoSite category
    pub async fn add_geosite_category(&self, entry: GeoSiteEntry) -> Result<()> {
        let mut categories = self.geosite_categories.write().await;
        categories.insert(entry.category.clone(), entry);
        info!("Added custom GeoSite category");
        Ok(())
    }

    /// Clear caches
    pub async fn clear_caches(&self) -> Result<()> {
        {
            let mut geoip_cache = self.geoip_cache.write().await;
            geoip_cache.clear();
        }
        {
            let mut domain_cache = self.domain_cache.write().await;
            domain_cache.clear();
        }
        info!("Cleared GeoIP and GeoSite caches");
        Ok(())
    }

    /// Get cache statistics
    pub async fn get_cache_stats(&self) -> Result<(usize, usize)> {
        let geoip_cache = self.geoip_cache.read().await;
        let domain_cache = self.domain_cache.read().await;
        Ok((geoip_cache.len(), domain_cache.len()))
    }
}
