use async_trait::async_trait;
use std::net::{Ip<PERSON>ddr, SocketAddr};
use std::collections::HashMap;
use anyhow::Result;
use serde::{Deserialize, Serialize};

/// Represents different proxy modes
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum ProxyMode {
    Global,
    Rules,
    Direct,
}

/// Represents a proxy node configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProxyNode {
    pub id: String,
    pub name: String,
    pub address: String,
    pub protocol: String,
    pub config: HashMap<String, String>,
}

/// Represents different types of proxy rules
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ProxyRule {
    DomainSuffix { suffix: String, node_id: String },
    Domain { domain: String, node_id: String },
    DomainKeyword { keyword: String, node_id: String },
    IpCidr { cidr: String, node_id: String },
    GeoIp { country: String, node_id: String },
    GeoSite { site: String, node_id: String },
}

/// Configuration structure downloaded from server
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProxyConfig {
    pub nodes: Vec<ProxyNode>,
    pub rules: Vec<ProxyRule>,
    pub version: String,
}

/// Traffic statistics
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct TrafficStats {
    pub upload_bytes: u64,
    pub download_bytes: u64,
    pub upload_speed: u64,  // bytes per second
    pub download_speed: u64, // bytes per second
}

/// Connection state information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionState {
    pub is_connected: bool,
    pub current_node: Option<String>,
    pub current_mode: ProxyMode,
    pub connection_time: Option<std::time::SystemTime>,
    pub stats: TrafficStats,
}

/// Core proxy management trait
#[async_trait]
pub trait CoreProxy: Send + Sync {
    /// Get list of available proxy node names
    async fn get_node_names(&self) -> Result<Vec<String>>;
    
    /// Get detailed information about a specific node
    async fn get_node_info(&self, node_id: &str) -> Result<Option<ProxyNode>>;
    
    /// Set the current proxy mode
    async fn set_proxy_mode(&self, mode: ProxyMode) -> Result<()>;
    
    /// Get the current proxy mode
    async fn get_proxy_mode(&self) -> Result<ProxyMode>;
    
    /// Select a proxy node for global mode and rule fallback
    async fn select_node(&self, node_id: &str) -> Result<()>;
    
    /// Get the currently selected node
    async fn get_selected_node(&self) -> Result<Option<String>>;
    
    /// Connect to the proxy service
    async fn connect(&self) -> Result<()>;
    
    /// Disconnect from the proxy service
    async fn disconnect(&self) -> Result<()>;
    
    /// Get current connection state
    async fn get_connection_state(&self) -> Result<ConnectionState>;
    
    /// Get current traffic statistics
    async fn get_traffic_stats(&self) -> Result<TrafficStats>;
}

/// Backend protocol integration trait for handling actual proxy connections
#[async_trait]
pub trait BackendProxy: Send + Sync {
    /// Handle a TCP stream through the proxy (legacy method for compatibility)
    async fn handle_tcp_stream(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
        data: Vec<u8>,
    ) -> Result<Vec<u8>>;

    /// Create or get a persistent TCP connection
    async fn get_or_create_tcp_connection(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
    ) -> Result<String>;

    /// Send data through an existing TCP connection
    async fn send_tcp_data(&self, connection_id: &str, data: Vec<u8>) -> Result<()>;

    /// Receive data from an existing TCP connection (non-blocking)
    async fn receive_tcp_data(&self, connection_id: &str) -> Result<Option<Vec<u8>>>;

    /// Close a specific TCP connection
    async fn close_tcp_connection(&self, connection_id: &str) -> Result<()>;

    /// Handle a UDP packet through the proxy
    async fn handle_udp_packet(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
        data: Vec<u8>,
    ) -> Result<Vec<u8>>;

    /// Establish a connection to a specific proxy node
    async fn connect_to_node(&self, node: &ProxyNode) -> Result<()>;

    /// Close connection to current proxy node
    async fn disconnect_from_node(&self) -> Result<()>;

    /// Test connectivity to a proxy node
    async fn test_node_connectivity(&self, node: &ProxyNode) -> Result<bool>;
}

/// Configuration management trait for encrypted config handling
#[async_trait]
pub trait ConfigManager: Send + Sync {
    /// Download and decrypt configuration from server
    async fn fetch_config(&self) -> Result<ProxyConfig>;
    
    /// Get the current configuration
    async fn get_current_config(&self) -> Result<Option<ProxyConfig>>;
    
    /// Validate configuration integrity
    async fn validate_config(&self, config: &ProxyConfig) -> Result<bool>;
    
    /// Get encryption key (hardcoded in implementation)
    fn get_encryption_key(&self) -> &[u8];
    
    /// Get config download URL (hardcoded in implementation)
    fn get_config_url(&self) -> &str;
}

/// Traffic routing decision trait
#[async_trait]
pub trait TrafficRouter: Send + Sync {
    /// Determine which proxy node should handle a request based on rules
    async fn route_request(
        &self,
        destination: &str,
        dest_ip: Option<IpAddr>,
        mode: ProxyMode,
    ) -> Result<Option<String>>;
    
    /// Check if a domain matches any rules
    async fn match_domain_rules(&self, domain: &str) -> Result<Option<String>>;
    
    /// Check if an IP matches any rules
    async fn match_ip_rules(&self, ip: IpAddr) -> Result<Option<String>>;
    
    /// Get the fallback node for rule mode
    async fn get_fallback_node(&self) -> Result<Option<String>>;
    
    /// Update routing rules
    async fn update_rules(&self, rules: Vec<ProxyRule>) -> Result<()>;

    /// Set the fallback node for rule mode
    async fn set_fallback_node(&self, node_id: Option<String>) -> Result<()>;
}

/// DNS resolution and FakeIP management trait
#[async_trait]
pub trait DnsManager: Send + Sync {
    /// Resolve a domain to a fake IP
    async fn resolve_to_fake_ip(&self, domain: &str) -> Result<IpAddr>;
    
    /// Get the original domain for a fake IP
    async fn get_domain_for_fake_ip(&self, ip: IpAddr) -> Result<Option<String>>;
    
    /// Handle DNS query and return fake IP response
    async fn handle_dns_query(&self, query: &[u8]) -> Result<Vec<u8>>;
    
    /// Check if an IP is in the fake IP range
    fn is_fake_ip(&self, ip: IpAddr) -> bool;
    
    /// Clear the fake IP mapping cache
    async fn clear_fake_ip_cache(&self) -> Result<()>;
}

/// System route management trait for cross-platform routing
#[async_trait]
pub trait RouteManager: Send + Sync {
    /// Add system route to direct traffic through TUN interface
    async fn add_route(&self, destination: &str, gateway: IpAddr) -> Result<()>;
    
    /// Remove system route
    async fn remove_route(&self, destination: &str) -> Result<()>;
    
    /// Get default gateway
    async fn get_default_gateway(&self) -> Result<IpAddr>;
    
    /// Set default route through TUN interface
    async fn set_default_route(&self, tun_ip: IpAddr) -> Result<()>;
    
    /// Restore original default route
    async fn restore_default_route(&self) -> Result<()>;
    
    /// Check if we have admin privileges for route modification
    fn has_admin_privileges(&self) -> bool;
    
    /// Request admin privileges if needed
    async fn request_admin_privileges(&self) -> Result<()>;
}
