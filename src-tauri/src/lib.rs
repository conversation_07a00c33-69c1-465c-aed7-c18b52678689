use std::sync::Arc;
use tokio::sync::RwLock;
use log::{info, error};
use tauri::Manager;

// Module declarations
mod traits;
mod tun;
mod packet;
mod dns;
mod rules;
mod config;
mod proxy;
mod socks5;
mod route;
mod test_backend;
mod stats;
mod protocol_manager;
mod custom_protocol_example;
mod geoip;
mod tcp_manager;

use traits::*;
use tun::TunManager;
use dns::FakeIpDnsManager;
use rules::RuleBasedTrafficRouter;
use config::EncryptedConfigManager;
use proxy::ProxyManager;
use test_backend::TestSocks5Backend;
use protocol_manager::ProtocolManager;

/// Main application state
pub struct AppState {
    proxy_manager: Arc<ProxyManager>,
    tun_manager: Arc<RwLock<TunManager>>,
    config_manager: Arc<EncryptedConfigManager>,
    dns_manager: Arc<FakeIpDnsManager>,
    traffic_router: Arc<RuleBasedTrafficRouter>,
    test_backend: Arc<TestSocks5Backend>,
    protocol_manager: Arc<ProtocolManager>,
}

impl AppState {
    pub async fn new() -> anyhow::Result<Self> {
        // Initialize managers
        let config_manager = Arc::new(EncryptedConfigManager::new());
        let dns_manager = Arc::new(FakeIpDnsManager::new());
        let traffic_router = Arc::new(RuleBasedTrafficRouter::new());

        // Download initial configuration
        info!("Downloading initial configuration...");
        match config_manager.fetch_config().await {
            Ok(config) => {
                info!("Successfully loaded initial configuration with {} nodes and {} rules",
                      config.nodes.len(), config.rules.len());

                // Update traffic router with rules
                if let Err(e) = traffic_router.update_rules(config.rules).await {
                    error!("Failed to update traffic rules: {}", e);
                }

                // Set fallback node if available
                if let Some(first_node) = config.nodes.first() {
                    if let Err(e) = traffic_router.set_fallback_node(Some(first_node.id.clone())).await {
                        error!("Failed to set fallback node: {}", e);
                    }
                }
            }
            Err(e) => {
                error!("Failed to load initial configuration: {}", e);
                info!("Application will continue with test configuration");
            }
        }

        // Initialize test backend
        let test_backend = test_backend::create_test_backend().await?;

        // Create protocol manager and set up default handlers
        let protocol_manager = Arc::new(ProtocolManager::new());
        protocol_manager.setup_default_handlers().await?;

        // Set up custom protocol handlers
        custom_protocol_example::setup_custom_protocols(&protocol_manager).await?;

        // Create proxy manager
        let proxy_manager = Arc::new(ProxyManager::new(
            Arc::clone(&config_manager) as Arc<dyn ConfigManager>,
            Arc::clone(&traffic_router) as Arc<dyn TrafficRouter>,
        ).await?);

        // Create TUN manager with protocol manager as backend
        let tun_manager = Arc::new(RwLock::new(TunManager::new(
            Arc::clone(&dns_manager) as Arc<dyn DnsManager>,
            Arc::clone(&traffic_router) as Arc<dyn TrafficRouter>,
            Arc::clone(&protocol_manager) as Arc<dyn BackendProxy>,
        )));

        let app_state = Self {
            proxy_manager,
            tun_manager,
            config_manager: config_manager.clone(),
            dns_manager,
            traffic_router: traffic_router.clone(),
            test_backend,
            protocol_manager,
        };

        // Start background config refresh task
        let config_manager_bg = config_manager.clone();
        let traffic_router_bg = traffic_router.clone();
        tokio::spawn(async move {
            Self::config_refresh_task(config_manager_bg, traffic_router_bg).await;
        });

        Ok(app_state)
    }

    /// Background task to periodically refresh configuration
    async fn config_refresh_task(
        config_manager: Arc<EncryptedConfigManager>,
        traffic_router: Arc<RuleBasedTrafficRouter>,
    ) {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(3600)); // Check every hour

        loop {
            interval.tick().await;

            if config_manager.should_refresh_config().await {
                info!("Automatic configuration refresh triggered");

                match config_manager.fetch_config().await {
                    Ok(config) => {
                        info!("Successfully refreshed configuration automatically with {} nodes and {} rules",
                              config.nodes.len(), config.rules.len());

                        // Update traffic router with new rules
                        if let Err(e) = traffic_router.update_rules(config.rules).await {
                            error!("Failed to update traffic rules during auto-refresh: {}", e);
                        }

                        // Update fallback node if available
                        if let Some(first_node) = config.nodes.first() {
                            if let Err(e) = traffic_router.set_fallback_node(Some(first_node.id.clone())).await {
                                error!("Failed to set fallback node during auto-refresh: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        error!("Failed to refresh configuration automatically: {}", e);
                    }
                }
            }
        }
    }
}

// Tauri commands
#[tauri::command]
async fn get_proxy_nodes(state: tauri::State<'_, AppState>) -> Result<Vec<String>, String> {
    state.proxy_manager.get_node_names().await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn set_proxy_mode(mode: String, state: tauri::State<'_, AppState>) -> Result<(), String> {
    let proxy_mode = match mode.as_str() {
        "global" => ProxyMode::Global,
        "rules" => ProxyMode::Rules,
        "direct" => ProxyMode::Direct,
        _ => return Err("Invalid proxy mode".to_string()),
    };

    state.proxy_manager.set_proxy_mode(proxy_mode).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn connect_proxy(state: tauri::State<'_, AppState>) -> Result<(), String> {
    // Start TUN interface
    {
        let mut tun = state.tun_manager.write().await;
        tun.start().await.map_err(|e| e.to_string())?;
    }

    // Connect proxy
    state.proxy_manager.connect().await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn disconnect_proxy(state: tauri::State<'_, AppState>) -> Result<(), String> {
    // Disconnect proxy
    state.proxy_manager.disconnect().await
        .map_err(|e| e.to_string())?;

    // Stop TUN interface
    {
        let mut tun = state.tun_manager.write().await;
        tun.stop().await.map_err(|e| e.to_string())?;
    }

    Ok(())
}

#[tauri::command]
async fn get_connection_state(state: tauri::State<'_, AppState>) -> Result<ConnectionState, String> {
    state.proxy_manager.get_connection_state().await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_traffic_stats(state: tauri::State<'_, AppState>) -> Result<TrafficStats, String> {
    state.proxy_manager.get_traffic_stats().await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn test_socks5_connection(state: tauri::State<'_, AppState>) -> Result<bool, String> {
    state.test_backend.test_connection().await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn test_http_request(
    host: String,
    path: String,
    state: tauri::State<'_, AppState>
) -> Result<String, String> {
    state.test_backend.test_http_request(&host, &path).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn refresh_config(state: tauri::State<'_, AppState>) -> Result<(), String> {
    info!("Manual configuration refresh requested");

    match state.config_manager.fetch_config().await {
        Ok(config) => {
            info!("Successfully refreshed configuration with {} nodes and {} rules",
                  config.nodes.len(), config.rules.len());

            // Update traffic router with new rules
            if let Err(e) = state.traffic_router.update_rules(config.rules).await {
                error!("Failed to update traffic rules: {}", e);
                return Err(format!("Failed to update traffic rules: {}", e));
            }

            // Update fallback node if available
            if let Some(first_node) = config.nodes.first() {
                if let Err(e) = state.traffic_router.set_fallback_node(Some(first_node.id.clone())).await {
                    error!("Failed to set fallback node: {}", e);
                }
            }

            Ok(())
        }
        Err(e) => {
            error!("Failed to refresh configuration: {}", e);
            Err(format!("Failed to refresh configuration: {}", e))
        }
    }
}

#[tauri::command]
async fn get_config_status(state: tauri::State<'_, AppState>) -> Result<serde_json::Value, String> {
    let current_config = state.config_manager.get_current_config().await
        .map_err(|e| e.to_string())?;

    let time_since_fetch = state.config_manager.time_since_last_fetch().await;
    let should_refresh = state.config_manager.should_refresh_config().await;

    let status = serde_json::json!({
        "has_config": current_config.is_some(),
        "node_count": current_config.as_ref().map(|c| c.nodes.len()).unwrap_or(0),
        "rule_count": current_config.as_ref().map(|c| c.rules.len()).unwrap_or(0),
        "version": current_config.as_ref().map(|c| &c.version).unwrap_or(&"unknown".to_string()),
        "time_since_fetch_seconds": time_since_fetch.map(|d| d.as_secs()),
        "should_refresh": should_refresh,
    });

    Ok(status)
}

#[tauri::command]
async fn test_protocol_connectivity(
    protocol_name: String,
    state: tauri::State<'_, AppState>
) -> Result<bool, String> {
    info!("Testing connectivity for protocol: {}", protocol_name);

    match state.protocol_manager.test_handler_connectivity(&protocol_name).await {
        Ok(result) => {
            info!("Protocol {} connectivity test: {}", protocol_name, if result { "PASSED" } else { "FAILED" });
            Ok(result)
        }
        Err(e) => {
            error!("Protocol {} connectivity test error: {}", protocol_name, e);
            Err(format!("Connectivity test failed: {}", e))
        }
    }
}

#[tauri::command]
async fn switch_protocol(
    protocol_name: String,
    state: tauri::State<'_, AppState>
) -> Result<(), String> {
    info!("Switching to protocol: {}", protocol_name);

    state.protocol_manager.set_current_handler(protocol_name.clone()).await
        .map_err(|e| format!("Failed to switch protocol: {}", e))?;

    info!("Successfully switched to protocol: {}", protocol_name);
    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .setup(|app| {
            // Setup logging
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Debug)
                        .build(),
                )?;
            }

            // Initialize app state
            let handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                match AppState::new().await {
                    Ok(state) => {
                        handle.manage(state);
                        info!("Application initialized successfully");
                    }
                    Err(e) => {
                        error!("Failed to initialize application: {}", e);
                    }
                }
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            get_proxy_nodes,
            set_proxy_mode,
            connect_proxy,
            disconnect_proxy,
            get_connection_state,
            get_traffic_stats,
            test_socks5_connection,
            test_http_request,
            refresh_config,
            get_config_status,
            test_protocol_connectivity,
            switch_protocol
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
