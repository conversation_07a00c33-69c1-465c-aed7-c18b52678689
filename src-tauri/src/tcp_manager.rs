use anyhow::{Result, anyhow};
use std::collections::HashMap;
use std::net::{IpAddr, SocketAddr};
use std::sync::Arc;
use std::time::{SystemTime, Duration};
use tokio::net::{Tcp<PERSON><PERSON>ener, TcpStream};
use tokio::sync::{mpsc, RwLock, Mutex};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio_socks::tcp::Socks5Stream;
use log::{debug, info, warn, error};

/// Represents a TCP connection state
#[derive(Debug, Clone)]
pub enum TcpConnectionState {
    Connecting,
    Established,
    Closing,
    Closed,
}

/// Information about an active TCP connection
#[derive(Debug, Clone)]
pub struct TcpConnectionInfo {
    pub local_addr: SocketAddr,
    pub remote_addr: SocketAddr,
    pub state: TcpConnectionState,
    pub created_at: SystemTime,
    pub last_activity: SystemTime,
    pub bytes_sent: u64,
    pub bytes_received: u64,
}

/// Handle for sending data to a TCP connection
pub struct TcpConnectionHandle {
    pub connection_id: String,
    pub data_sender: mpsc::UnboundedSender<Vec<u8>>,
    pub response_receiver: Arc<Mutex<mpsc::UnboundedReceiver<Vec<u8>>>>,
}

/// TCP connection manager that maintains persistent connections
pub struct TcpConnectionManager {
    connections: Arc<RwLock<HashMap<String, TcpConnectionInfo>>>,
    connection_handles: Arc<RwLock<HashMap<String, TcpConnectionHandle>>>,
    proxy_config: Arc<RwLock<Option<ProxyConfig>>>,
    connection_timeout: Duration,
    idle_timeout: Duration,
}

/// Proxy configuration for TCP connections
#[derive(Debug, Clone)]
pub struct ProxyConfig {
    pub addr: String,
    pub username: Option<String>,
    pub password: Option<String>,
}

impl TcpConnectionManager {
    pub fn new() -> Self {
        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            connection_handles: Arc::new(RwLock::new(HashMap::new())),
            proxy_config: Arc::new(RwLock::new(None)),
            connection_timeout: Duration::from_secs(30),
            idle_timeout: Duration::from_secs(300), // 5 minutes
        }
    }

    /// Configure SOCKS5 proxy settings
    pub async fn configure_proxy(&self, addr: String, username: Option<String>, password: Option<String>) {
        let mut config = self.proxy_config.write().await;
        *config = Some(ProxyConfig {
            addr,
            username,
            password,
        });
    }

    /// Get or create a TCP connection for the given addresses
    pub async fn get_or_create_connection(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
    ) -> Result<String> {
        let connection_id = format!("{}->{}", local_addr, remote_addr);
        
        // Check if connection already exists
        {
            let connections = self.connections.read().await;
            if let Some(conn_info) = connections.get(&connection_id) {
                if matches!(conn_info.state, TcpConnectionState::Established) {
                    debug!("Reusing existing TCP connection: {}", connection_id);
                    return Ok(connection_id);
                }
            }
        }

        // Create new connection
        debug!("Creating new TCP connection: {}", connection_id);
        self.create_connection(connection_id.clone(), local_addr, remote_addr).await?;
        
        Ok(connection_id)
    }

    /// Create a new TCP connection
    async fn create_connection(
        &self,
        connection_id: String,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
    ) -> Result<()> {
        let (data_tx, mut data_rx) = mpsc::unbounded_channel::<Vec<u8>>();
        let (response_tx, response_rx) = mpsc::unbounded_channel::<Vec<u8>>();

        // Store connection info
        {
            let mut connections = self.connections.write().await;
            connections.insert(connection_id.clone(), TcpConnectionInfo {
                local_addr,
                remote_addr,
                state: TcpConnectionState::Connecting,
                created_at: SystemTime::now(),
                last_activity: SystemTime::now(),
                bytes_sent: 0,
                bytes_received: 0,
            });
        }

        // Store connection handle
        {
            let mut handles = self.connection_handles.write().await;
            handles.insert(connection_id.clone(), TcpConnectionHandle {
                connection_id: connection_id.clone(),
                data_sender: data_tx,
                response_receiver: Arc::new(Mutex::new(response_rx)),
            });
        }

        // Spawn connection task
        let connections_clone = Arc::clone(&self.connections);
        let handles_clone = Arc::clone(&self.connection_handles);
        let proxy_config_clone = Arc::clone(&self.proxy_config);
        let connection_id_clone = connection_id.clone();

        tokio::spawn(async move {
            if let Err(e) = Self::handle_connection(
                connection_id_clone.clone(),
                remote_addr,
                data_rx,
                response_tx,
                proxy_config_clone,
                connections_clone.clone(),
            ).await {
                error!("TCP connection {} failed: {}", connection_id_clone, e);
                
                // Mark connection as closed
                {
                    let mut connections = connections_clone.write().await;
                    if let Some(conn_info) = connections.get_mut(&connection_id_clone) {
                        conn_info.state = TcpConnectionState::Closed;
                    }
                }

                // Remove connection handle
                {
                    let mut handles = handles_clone.write().await;
                    handles.remove(&connection_id_clone);
                }
            }
        });

        Ok(())
    }

    /// Handle a TCP connection with bidirectional streaming
    async fn handle_connection(
        connection_id: String,
        remote_addr: SocketAddr,
        mut data_rx: mpsc::UnboundedReceiver<Vec<u8>>,
        response_tx: mpsc::UnboundedSender<Vec<u8>>,
        proxy_config: Arc<RwLock<Option<ProxyConfig>>>,
        connections: Arc<RwLock<HashMap<String, TcpConnectionInfo>>>,
    ) -> Result<()> {
        // Get proxy configuration
        let proxy_config_guard = proxy_config.read().await;
        let proxy_config_clone = proxy_config_guard.clone();
        drop(proxy_config_guard);

        // Establish connection (either direct or through SOCKS5)
        let stream = if let Some(config) = proxy_config_clone {
            debug!("Establishing SOCKS5 connection to {} via {}", remote_addr, config.addr);

            let proxy_socket_addr = config.addr.parse::<SocketAddr>()
                .map_err(|e| anyhow!("Invalid proxy address {}: {}", config.addr, e))?;

            let socks5_stream = if let (Some(username), Some(password)) = (&config.username, &config.password) {
                Socks5Stream::connect_with_password(
                    proxy_socket_addr,
                    remote_addr.to_string(),
                    username,
                    password,
                ).await?
            } else {
                Socks5Stream::connect(proxy_socket_addr, remote_addr.to_string()).await?
            };

            Box::new(socks5_stream)
        } else {
            debug!("Establishing direct TCP connection to {}", remote_addr);
            let tcp_stream = TcpStream::connect(remote_addr).await?;
            Box::new(tcp_stream)
        };

        // Mark connection as established
        {
            let mut connections_guard = connections.write().await;
            if let Some(conn_info) = connections_guard.get_mut(&connection_id) {
                conn_info.state = TcpConnectionState::Established;
                conn_info.last_activity = SystemTime::now();
            }
        }

        info!("TCP connection established: {}", connection_id);

        // Split stream for bidirectional communication
        let (mut read_half, mut write_half) = tokio::io::split(stream);

        // Spawn task to handle incoming data from TUN and send to remote
        let connection_id_write = connection_id.clone();
        let connections_write = Arc::clone(&connections);
        let write_task = tokio::spawn(async move {
            while let Some(data) = data_rx.recv().await {
                debug!("Sending {} bytes to remote for connection {}", data.len(), connection_id_write);
                
                if let Err(e) = write_half.write_all(&data).await {
                    error!("Failed to write to TCP stream {}: {}", connection_id_write, e);
                    break;
                }

                // Update statistics
                {
                    let mut connections_guard = connections_write.write().await;
                    if let Some(conn_info) = connections_guard.get_mut(&connection_id_write) {
                        conn_info.bytes_sent += data.len() as u64;
                        conn_info.last_activity = SystemTime::now();
                    }
                }
            }
            debug!("Write task ended for connection {}", connection_id_write);
        });

        // Spawn task to handle incoming data from remote and send to TUN
        let connection_id_read = connection_id.clone();
        let connections_read = Arc::clone(&connections);
        let read_task = tokio::spawn(async move {
            let mut buffer = vec![0u8; 4096];
            
            loop {
                match read_half.read(&mut buffer).await {
                    Ok(0) => {
                        debug!("Remote closed connection {}", connection_id_read);
                        break;
                    }
                    Ok(n) => {
                        let data = buffer[..n].to_vec();
                        debug!("Received {} bytes from remote for connection {}", n, connection_id_read);
                        
                        if response_tx.send(data).is_err() {
                            debug!("Response channel closed for connection {}", connection_id_read);
                            break;
                        }

                        // Update statistics
                        {
                            let mut connections_guard = connections_read.write().await;
                            if let Some(conn_info) = connections_guard.get_mut(&connection_id_read) {
                                conn_info.bytes_received += n as u64;
                                conn_info.last_activity = SystemTime::now();
                            }
                        }
                    }
                    Err(e) => {
                        error!("Failed to read from TCP stream {}: {}", connection_id_read, e);
                        break;
                    }
                }
            }
            debug!("Read task ended for connection {}", connection_id_read);
        });

        // Wait for either task to complete
        tokio::select! {
            _ = write_task => {
                debug!("Write task completed for connection {}", connection_id);
            }
            _ = read_task => {
                debug!("Read task completed for connection {}", connection_id);
            }
        }

        // Mark connection as closed
        {
            let mut connections_guard = connections.write().await;
            if let Some(conn_info) = connections_guard.get_mut(&connection_id) {
                conn_info.state = TcpConnectionState::Closed;
            }
        }

        info!("TCP connection closed: {}", connection_id);
        Ok(())
    }

    /// Send data through an existing TCP connection
    pub async fn send_data(&self, connection_id: &str, data: Vec<u8>) -> Result<()> {
        let handles = self.connection_handles.read().await;
        if let Some(handle) = handles.get(connection_id) {
            handle.data_sender.send(data)
                .map_err(|_| anyhow!("Failed to send data to connection {}", connection_id))?;
            Ok(())
        } else {
            Err(anyhow!("Connection {} not found", connection_id))
        }
    }

    /// Receive data from an existing TCP connection (non-blocking)
    pub async fn receive_data(&self, connection_id: &str) -> Result<Option<Vec<u8>>> {
        let handles = self.connection_handles.read().await;
        if let Some(handle) = handles.get(connection_id) {
            let mut receiver = handle.response_receiver.lock().await;
            match receiver.try_recv() {
                Ok(data) => Ok(Some(data)),
                Err(mpsc::error::TryRecvError::Empty) => Ok(None),
                Err(mpsc::error::TryRecvError::Disconnected) => {
                    Err(anyhow!("Connection {} is closed", connection_id))
                }
            }
        } else {
            Err(anyhow!("Connection {} not found", connection_id))
        }
    }

    /// Get connection information
    pub async fn get_connection_info(&self, connection_id: &str) -> Option<TcpConnectionInfo> {
        let connections = self.connections.read().await;
        connections.get(connection_id).cloned()
    }

    /// Close a specific connection
    pub async fn close_connection(&self, connection_id: &str) -> Result<()> {
        debug!("Closing TCP connection: {}", connection_id);
        
        // Remove connection handle (this will cause the connection task to end)
        {
            let mut handles = self.connection_handles.write().await;
            handles.remove(connection_id);
        }

        // Mark connection as closed
        {
            let mut connections = self.connections.write().await;
            if let Some(conn_info) = connections.get_mut(connection_id) {
                conn_info.state = TcpConnectionState::Closed;
            }
        }

        Ok(())
    }

    /// Clean up old and inactive connections
    pub async fn cleanup_old_connections(&self) {
        let now = SystemTime::now();
        let mut to_remove = Vec::new();

        {
            let connections = self.connections.read().await;
            for (connection_id, conn_info) in connections.iter() {
                let should_remove = match conn_info.state {
                    TcpConnectionState::Closed => true,
                    _ => {
                        if let Ok(elapsed) = now.duration_since(conn_info.last_activity) {
                            elapsed > self.idle_timeout
                        } else {
                            false
                        }
                    }
                };

                if should_remove {
                    to_remove.push(connection_id.clone());
                }
            }
        }

        for connection_id in to_remove {
            debug!("Cleaning up old TCP connection: {}", connection_id);
            let _ = self.close_connection(&connection_id).await;
            
            // Remove from connections map
            {
                let mut connections = self.connections.write().await;
                connections.remove(&connection_id);
            }
        }
    }

    /// Get all active connections
    pub async fn get_active_connections(&self) -> Vec<String> {
        let connections = self.connections.read().await;
        connections.iter()
            .filter(|(_, info)| matches!(info.state, TcpConnectionState::Established))
            .map(|(id, _)| id.clone())
            .collect()
    }
}
