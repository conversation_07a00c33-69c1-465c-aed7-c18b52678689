use anyhow::{Result, anyhow};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::SystemTime;
use tokio::sync::RwLock;
use async_trait::async_trait;
use log::{info, warn, error, debug};

use crate::traits::{
    CoreProxy, BackendProxy, Config<PERSON>anager, TrafficRouter,
    ProxyMode, ProxyNode, ConnectionState, TrafficStats
};
use crate::socks5::Socks5Client;
use crate::stats::get_global_stats;

/// Main proxy manager that implements both CoreProxy and BackendProxy traits
pub struct ProxyManager {
    /// Configuration manager
    config_manager: Arc<dyn ConfigManager>,
    /// Traffic router
    traffic_router: Arc<dyn TrafficRouter>,
    /// Current proxy mode
    current_mode: RwLock<ProxyMode>,
    /// Currently selected node
    selected_node: RwLock<Option<String>>,
    /// Connection state
    is_connected: RwLock<bool>,
    /// Connection start time
    connection_time: RwLock<Option<SystemTime>>,
    /// Traffic statistics
    traffic_stats: RwLock<TrafficStats>,
    /// SOCKS5 client for testing
    socks5_client: RwLock<Option<Socks5Client>>,
}

impl ProxyManager {
    pub async fn new(
        config_manager: Arc<dyn ConfigManager>,
        traffic_router: Arc<dyn TrafficRouter>,
    ) -> Result<Self> {
        let manager = Self {
            config_manager,
            traffic_router,
            current_mode: RwLock::new(ProxyMode::Global),
            selected_node: RwLock::new(None),
            is_connected: RwLock::new(false),
            connection_time: RwLock::new(None),
            traffic_stats: RwLock::new(TrafficStats::default()),
            socks5_client: RwLock::new(None),
        };

        // Load initial configuration
        manager.load_config().await?;

        Ok(manager)
    }

    /// Load configuration and update routing rules
    async fn load_config(&self) -> Result<()> {
        info!("Loading proxy configuration...");
        
        let config = self.config_manager.fetch_config().await?;
        
        // Update routing rules
        self.traffic_router.update_rules(config.rules).await?;
        
        // Set first node as default if none selected
        if config.nodes.is_empty() {
            warn!("No proxy nodes available in configuration");
        } else {
            let mut selected = self.selected_node.write().await;
            if selected.is_none() {
                *selected = Some(config.nodes[0].id.clone());
                info!("Selected default node: {}", config.nodes[0].name);
                
                // Set as fallback node for traffic router
                let _ = self.traffic_router.set_fallback_node(Some(config.nodes[0].id.clone())).await;
            }
        }
        
        info!("Configuration loaded successfully");
        Ok(())
    }

    /// Get node by ID
    async fn get_node_by_id(&self, node_id: &str) -> Result<Option<ProxyNode>> {
        if let Some(config) = self.config_manager.get_current_config().await? {
            for node in config.nodes {
                if node.id == node_id {
                    return Ok(Some(node));
                }
            }
        }
        Ok(None)
    }

    /// Update traffic statistics
    async fn update_traffic_stats(&self, upload_bytes: u64, download_bytes: u64) {
        let mut stats = self.traffic_stats.write().await;
        stats.upload_bytes += upload_bytes;
        stats.download_bytes += download_bytes;
        
        // Simple speed calculation (would need more sophisticated implementation)
        stats.upload_speed = upload_bytes; // bytes in last operation
        stats.download_speed = download_bytes; // bytes in last operation
    }
}

#[async_trait]
impl CoreProxy for ProxyManager {
    async fn get_node_names(&self) -> Result<Vec<String>> {
        if let Some(config) = self.config_manager.get_current_config().await? {
            Ok(config.nodes.into_iter().map(|n| n.name).collect())
        } else {
            Ok(Vec::new())
        }
    }

    async fn get_node_info(&self, node_id: &str) -> Result<Option<ProxyNode>> {
        self.get_node_by_id(node_id).await
    }

    async fn set_proxy_mode(&self, mode: ProxyMode) -> Result<()> {
        let mut current_mode = self.current_mode.write().await;
        *current_mode = mode;
        info!("Proxy mode changed to: {:?}", mode);
        Ok(())
    }

    async fn get_proxy_mode(&self) -> Result<ProxyMode> {
        let mode = self.current_mode.read().await;
        Ok(*mode)
    }

    async fn select_node(&self, node_id: &str) -> Result<()> {
        // Verify node exists
        if self.get_node_by_id(node_id).await?.is_none() {
            return Err(anyhow!("Node not found: {}", node_id));
        }

        let mut selected = self.selected_node.write().await;
        *selected = Some(node_id.to_string());
        
        // Update traffic router fallback
        let _ = self.traffic_router.set_fallback_node(Some(node_id.to_string())).await;
        
        info!("Selected proxy node: {}", node_id);
        Ok(())
    }

    async fn get_selected_node(&self) -> Result<Option<String>> {
        let selected = self.selected_node.read().await;
        Ok(selected.clone())
    }

    async fn connect(&self) -> Result<()> {
        let selected_node_id = {
            let selected = self.selected_node.read().await;
            selected.clone()
        };

        let node_id = selected_node_id.ok_or_else(|| anyhow!("No node selected"))?;
        let node = self.get_node_by_id(&node_id).await?
            .ok_or_else(|| anyhow!("Selected node not found"))?;

        info!("Connecting to proxy node: {}", node.name);

        // Connect to the node
        self.connect_to_node(&node).await?;

        // Update connection state
        {
            let mut connected = self.is_connected.write().await;
            *connected = true;
        }
        {
            let mut conn_time = self.connection_time.write().await;
            *conn_time = Some(SystemTime::now());
        }

        info!("Successfully connected to proxy");
        Ok(())
    }

    async fn disconnect(&self) -> Result<()> {
        info!("Disconnecting from proxy...");

        // Disconnect from current node
        self.disconnect_from_node().await?;

        // Update connection state
        {
            let mut connected = self.is_connected.write().await;
            *connected = false;
        }
        {
            let mut conn_time = self.connection_time.write().await;
            *conn_time = None;
        }

        info!("Disconnected from proxy");
        Ok(())
    }

    async fn get_connection_state(&self) -> Result<ConnectionState> {
        let is_connected = *self.is_connected.read().await;
        let current_mode = *self.current_mode.read().await;
        let connection_time = *self.connection_time.read().await;

        // Get real traffic statistics
        let real_stats = self.get_traffic_stats().await?;

        let current_node = if is_connected {
            let selected = self.selected_node.read().await;
            if let Some(node_id) = selected.as_ref() {
                if let Ok(Some(node)) = self.get_node_by_id(node_id).await {
                    Some(node.name)
                } else {
                    None
                }
            } else {
                None
            }
        } else {
            None
        };

        Ok(ConnectionState {
            is_connected,
            current_node,
            current_mode,
            connection_time,
            stats: real_stats,
        })
    }

    async fn get_traffic_stats(&self) -> Result<TrafficStats> {
        // Get real traffic statistics from global tracker
        let global_stats = get_global_stats();
        let real_stats = global_stats.get_stats().await;

        // Convert to the expected format
        Ok(TrafficStats {
            upload_bytes: real_stats.total_upload,
            download_bytes: real_stats.total_download,
            upload_speed: real_stats.upload_speed,
            download_speed: real_stats.download_speed,
        })
    }
}

#[async_trait]
impl BackendProxy for ProxyManager {
    async fn handle_tcp_stream(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
        data: Vec<u8>,
    ) -> Result<Vec<u8>> {
        debug!("Handling TCP stream: {} -> {}", local_addr, remote_addr);

        // Update traffic stats
        self.update_traffic_stats(data.len() as u64, 0).await;

        // For now, use SOCKS5 client if available
        let socks5_client = self.socks5_client.read().await;
        if let Some(client) = socks5_client.as_ref() {
            let response = client.handle_tcp_data(remote_addr, &data).await?;
            self.update_traffic_stats(0, response.len() as u64).await;
            Ok(response)
        } else {
            // No proxy connection, return empty (direct connection)
            Ok(Vec::new())
        }
    }

    async fn handle_udp_packet(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
        data: Vec<u8>,
    ) -> Result<Vec<u8>> {
        debug!("Handling UDP packet: {} -> {}", local_addr, remote_addr);

        // Update traffic stats
        self.update_traffic_stats(data.len() as u64, 0).await;

        // For now, use SOCKS5 client if available
        let socks5_client = self.socks5_client.read().await;
        if let Some(client) = socks5_client.as_ref() {
            let response = client.handle_udp_data(remote_addr, &data).await?;
            self.update_traffic_stats(0, response.len() as u64).await;
            Ok(response)
        } else {
            // No proxy connection, return empty (direct connection)
            Ok(Vec::new())
        }
    }

    async fn connect_to_node(&self, node: &ProxyNode) -> Result<()> {
        info!("Connecting to node: {} ({})", node.name, node.address);

        match node.protocol.as_str() {
            "socks5" => {
                let username = node.config.get("username").cloned();
                let password = node.config.get("password").cloned();
                
                let client = Socks5Client::new(&node.address, username, password).await?;
                
                let mut socks5_client = self.socks5_client.write().await;
                *socks5_client = Some(client);
                
                info!("Connected to SOCKS5 proxy: {}", node.address);
            }
            "custom" => {
                // This is where you would integrate with your custom protocol
                info!("Custom protocol connection not implemented yet");
                // For now, just mark as connected
            }
            _ => {
                return Err(anyhow!("Unsupported protocol: {}", node.protocol));
            }
        }

        Ok(())
    }

    async fn disconnect_from_node(&self) -> Result<()> {
        let mut socks5_client = self.socks5_client.write().await;
        if socks5_client.is_some() {
            *socks5_client = None;
            info!("Disconnected from SOCKS5 proxy");
        }
        Ok(())
    }

    async fn test_node_connectivity(&self, node: &ProxyNode) -> Result<bool> {
        info!("Testing connectivity to node: {}", node.name);

        match node.protocol.as_str() {
            "socks5" => {
                let username = node.config.get("username").cloned();
                let password = node.config.get("password").cloned();
                
                match Socks5Client::new(&node.address, username, password).await {
                    Ok(_) => {
                        info!("Node {} is reachable", node.name);
                        Ok(true)
                    }
                    Err(e) => {
                        warn!("Node {} is not reachable: {}", node.name, e);
                        Ok(false)
                    }
                }
            }
            _ => {
                // For other protocols, assume reachable for now
                Ok(true)
            }
        }
    }
}
