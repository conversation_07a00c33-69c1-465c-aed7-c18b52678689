use anyhow::{Result, anyhow};
use std::net::SocketAddr;
use std::sync::Arc;
use std::collections::HashMap;
use async_trait::async_trait;
use tokio::sync::RwLock;
use log::{info, warn, debug, error};

use crate::traits::{BackendProxy, ProxyNode};
use crate::socks5::Socks5Client;
use crate::tcp_manager::TcpConnectionManager;

/// Protocol type enumeration
#[derive(Debug, Clone, PartialEq)]
pub enum ProtocolType {
    Socks5,
    Custom(String), // Custom protocol with identifier
    Http,
    Https,
    Shadowsocks,
    Vmess,
    Trojan,
}

impl ProtocolType {
    pub fn from_string(protocol: &str) -> Self {
        match protocol.to_lowercase().as_str() {
            "socks5" => ProtocolType::Socks5,
            "http" => ProtocolType::Http,
            "https" => ProtocolType::Https,
            "shadowsocks" | "ss" => ProtocolType::Shadowsocks,
            "vmess" => ProtocolType::Vmess,
            "trojan" => ProtocolType::Trojan,
            custom => ProtocolType::Custom(custom.to_string()),
        }
    }

    pub fn to_string(&self) -> String {
        match self {
            ProtocolType::Socks5 => "socks5".to_string(),
            ProtocolType::Http => "http".to_string(),
            ProtocolType::Https => "https".to_string(),
            ProtocolType::Shadowsocks => "shadowsocks".to_string(),
            ProtocolType::Vmess => "vmess".to_string(),
            ProtocolType::Trojan => "trojan".to_string(),
            ProtocolType::Custom(name) => name.clone(),
        }
    }
}

/// Protocol handler trait for different backend protocols
#[async_trait]
pub trait ProtocolHandler: Send + Sync {
    /// Initialize the protocol handler with node configuration
    async fn initialize(&mut self, node: &ProxyNode) -> Result<()>;
    
    /// Handle TCP data through this protocol
    async fn handle_tcp_data(&self, remote_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>>;
    
    /// Handle UDP data through this protocol
    async fn handle_udp_data(&self, remote_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>>;
    
    /// Test connectivity for this protocol
    async fn test_connectivity(&self) -> Result<bool>;
    
    /// Close the connection
    async fn close(&mut self) -> Result<()>;
    
    /// Get protocol type
    fn protocol_type(&self) -> ProtocolType;

    /// Enable downcasting to concrete types
    fn as_any(&self) -> &dyn std::any::Any;
}

/// SOCKS5 protocol handler
pub struct Socks5Handler {
    client: Option<Socks5Client>,
    node_config: Option<ProxyNode>,
}

impl Socks5Handler {
    pub fn new() -> Self {
        Self {
            client: None,
            node_config: None,
        }
    }
}

#[async_trait]
impl ProtocolHandler for Socks5Handler {
    async fn initialize(&mut self, node: &ProxyNode) -> Result<()> {
        info!("Initializing SOCKS5 handler for node: {}", node.name);
        
        let username = node.config.get("username").cloned();
        let password = node.config.get("password").cloned();
        
        let client = Socks5Client::new(&node.address, username, password).await?;
        self.client = Some(client);
        self.node_config = Some(node.clone());
        
        info!("SOCKS5 handler initialized successfully");
        Ok(())
    }
    
    async fn handle_tcp_data(&self, remote_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>> {
        if let Some(client) = &self.client {
            client.handle_tcp_data(remote_addr, data).await
        } else {
            Err(anyhow!("SOCKS5 client not initialized"))
        }
    }
    
    async fn handle_udp_data(&self, remote_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>> {
        if let Some(client) = &self.client {
            client.handle_udp_data(remote_addr, data).await
        } else {
            Err(anyhow!("SOCKS5 client not initialized"))
        }
    }
    
    async fn test_connectivity(&self) -> Result<bool> {
        if let Some(client) = &self.client {
            client.test_http_connectivity().await
        } else {
            Ok(false)
        }
    }
    
    async fn close(&mut self) -> Result<()> {
        self.client = None;
        self.node_config = None;
        Ok(())
    }
    
    fn protocol_type(&self) -> ProtocolType {
        ProtocolType::Socks5
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

/// Custom protocol handler interface
pub struct CustomProtocolHandler {
    protocol_name: String,
    node_config: Option<ProxyNode>,
    // Add custom protocol specific fields here
    custom_client: Option<Box<dyn CustomProtocolClient>>,
}

/// Trait for custom protocol implementations
#[async_trait]
pub trait CustomProtocolClient: Send + Sync {
    async fn connect(&mut self, node: &ProxyNode) -> Result<()>;
    async fn send_tcp(&self, remote_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>>;
    async fn send_udp(&self, remote_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>>;
    async fn test_connection(&self) -> Result<bool>;
    async fn disconnect(&mut self) -> Result<()>;
}

impl CustomProtocolHandler {
    pub fn new(protocol_name: String) -> Self {
        Self {
            protocol_name,
            node_config: None,
            custom_client: None,
        }
    }
    
    /// Set custom protocol client implementation
    pub fn set_custom_client(&mut self, client: Box<dyn CustomProtocolClient>) {
        self.custom_client = Some(client);
    }
}

#[async_trait]
impl ProtocolHandler for CustomProtocolHandler {
    async fn initialize(&mut self, node: &ProxyNode) -> Result<()> {
        info!("Initializing custom protocol handler: {} for node: {}", self.protocol_name, node.name);
        
        if let Some(client) = &mut self.custom_client {
            client.connect(node).await?;
            self.node_config = Some(node.clone());
            info!("Custom protocol handler initialized successfully");
            Ok(())
        } else {
            Err(anyhow!("Custom protocol client not set"))
        }
    }
    
    async fn handle_tcp_data(&self, remote_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>> {
        if let Some(client) = &self.custom_client {
            client.send_tcp(remote_addr, data).await
        } else {
            Err(anyhow!("Custom protocol client not initialized"))
        }
    }
    
    async fn handle_udp_data(&self, remote_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>> {
        if let Some(client) = &self.custom_client {
            client.send_udp(remote_addr, data).await
        } else {
            Err(anyhow!("Custom protocol client not initialized"))
        }
    }
    
    async fn test_connectivity(&self) -> Result<bool> {
        if let Some(client) = &self.custom_client {
            client.test_connection().await
        } else {
            Ok(false)
        }
    }
    
    async fn close(&mut self) -> Result<()> {
        if let Some(client) = &mut self.custom_client {
            client.disconnect().await?;
        }
        self.custom_client = None;
        self.node_config = None;
        Ok(())
    }
    
    fn protocol_type(&self) -> ProtocolType {
        ProtocolType::Custom(self.protocol_name.clone())
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

/// Protocol manager that handles multiple backend protocols
pub struct ProtocolManager {
    handlers: RwLock<HashMap<String, Box<dyn ProtocolHandler>>>,
    current_handler: RwLock<Option<String>>,
    fallback_handler: RwLock<Option<String>>,
    tcp_manager: Arc<TcpConnectionManager>,
}

impl ProtocolManager {
    pub fn new() -> Self {
        Self {
            handlers: RwLock::new(HashMap::new()),
            current_handler: RwLock::new(None),
            fallback_handler: RwLock::new(None),
            tcp_manager: Arc::new(TcpConnectionManager::new()),
        }
    }
    
    /// Register a protocol handler
    pub async fn register_handler(&self, name: String, handler: Box<dyn ProtocolHandler>) -> Result<()> {
        let mut handlers = self.handlers.write().await;
        handlers.insert(name.clone(), handler);
        info!("Registered protocol handler: {}", name);
        Ok(())
    }
    
    /// Set the current active handler
    pub async fn set_current_handler(&self, name: String) -> Result<()> {
        let handlers = self.handlers.read().await;
        if handlers.contains_key(&name) {
            let mut current = self.current_handler.write().await;
            *current = Some(name.clone());
            info!("Set current protocol handler: {}", name);
            Ok(())
        } else {
            Err(anyhow!("Protocol handler not found: {}", name))
        }
    }
    
    /// Set fallback handler (usually SOCKS5)
    pub async fn set_fallback_handler(&self, name: String) -> Result<()> {
        let handlers = self.handlers.read().await;
        if handlers.contains_key(&name) {
            let mut fallback = self.fallback_handler.write().await;
            *fallback = Some(name.clone());
            info!("Set fallback protocol handler: {}", name);
            Ok(())
        } else {
            Err(anyhow!("Fallback protocol handler not found: {}", name))
        }
    }
    
    /// Get current handler or fallback
    async fn get_active_handler(&self) -> Option<String> {
        let current = self.current_handler.read().await;
        if current.is_some() {
            current.clone()
        } else {
            let fallback = self.fallback_handler.read().await;
            fallback.clone()
        }
    }

    /// Initialize a handler with a node
    pub async fn initialize_handler(&self, handler_name: &str, node: &ProxyNode) -> Result<()> {
        let mut handlers = self.handlers.write().await;
        if let Some(handler) = handlers.get_mut(handler_name) {
            handler.initialize(node).await?;
            info!("Initialized handler {} with node {}", handler_name, node.name);
            Ok(())
        } else {
            Err(anyhow!("Handler not found: {}", handler_name))
        }
    }

    /// Close a handler
    pub async fn close_handler(&self, handler_name: &str) -> Result<()> {
        let mut handlers = self.handlers.write().await;
        if let Some(handler) = handlers.get_mut(handler_name) {
            handler.close().await?;
            info!("Closed handler: {}", handler_name);
            Ok(())
        } else {
            Err(anyhow!("Handler not found: {}", handler_name))
        }
    }

    /// Test connectivity for a handler
    pub async fn test_handler_connectivity(&self, handler_name: &str) -> Result<bool> {
        let handlers = self.handlers.read().await;
        if let Some(handler) = handlers.get(handler_name) {
            handler.test_connectivity().await
        } else {
            Err(anyhow!("Handler not found: {}", handler_name))
        }
    }

    /// Create default handlers (SOCKS5 fallback)
    pub async fn setup_default_handlers(&self) -> Result<()> {
        // Register SOCKS5 as default fallback
        let socks5_handler = Box::new(Socks5Handler::new());
        self.register_handler("socks5".to_string(), socks5_handler).await?;
        self.set_fallback_handler("socks5".to_string()).await?;

        info!("Default protocol handlers setup completed");
        Ok(())
    }
}

#[async_trait]
impl BackendProxy for ProtocolManager {
    async fn handle_tcp_stream(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
        data: Vec<u8>,
    ) -> Result<Vec<u8>> {
        debug!("Handling TCP stream: {} -> {} ({} bytes)", local_addr, remote_addr, data.len());

        if let Some(handler_name) = self.get_active_handler().await {
            let handlers = self.handlers.read().await;
            if let Some(handler) = handlers.get(&handler_name) {
                match handler.handle_tcp_data(remote_addr, &data).await {
                    Ok(response) => {
                        debug!("TCP response from {}: {} bytes", handler_name, response.len());
                        return Ok(response);
                    }
                    Err(e) => {
                        warn!("TCP handling failed with {}: {}", handler_name, e);

                        // Try fallback if current handler failed and it's not the fallback
                        if let Some(fallback_name) = self.fallback_handler.read().await.clone() {
                            if fallback_name != handler_name {
                                if let Some(fallback_handler) = handlers.get(&fallback_name) {
                                    warn!("Trying fallback handler: {}", fallback_name);
                                    match fallback_handler.handle_tcp_data(remote_addr, &data).await {
                                        Ok(response) => {
                                            info!("Fallback TCP handling succeeded");
                                            return Ok(response);
                                        }
                                        Err(fallback_err) => {
                                            error!("Fallback TCP handling also failed: {}", fallback_err);
                                        }
                                    }
                                }
                            }
                        }

                        return Err(e);
                    }
                }
            }
        }

        warn!("No active protocol handler available for TCP");
        Ok(Vec::new())
    }

    async fn handle_udp_packet(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
        data: Vec<u8>,
    ) -> Result<Vec<u8>> {
        debug!("Handling UDP packet: {} -> {} ({} bytes)", local_addr, remote_addr, data.len());

        if let Some(handler_name) = self.get_active_handler().await {
            let handlers = self.handlers.read().await;
            if let Some(handler) = handlers.get(&handler_name) {
                match handler.handle_udp_data(remote_addr, &data).await {
                    Ok(response) => {
                        debug!("UDP response from {}: {} bytes", handler_name, response.len());
                        return Ok(response);
                    }
                    Err(e) => {
                        warn!("UDP handling failed with {}: {}", handler_name, e);

                        // Try fallback if current handler failed and it's not the fallback
                        if let Some(fallback_name) = self.fallback_handler.read().await.clone() {
                            if fallback_name != handler_name {
                                if let Some(fallback_handler) = handlers.get(&fallback_name) {
                                    warn!("Trying fallback handler: {}", fallback_name);
                                    match fallback_handler.handle_udp_data(remote_addr, &data).await {
                                        Ok(response) => {
                                            info!("Fallback UDP handling succeeded");
                                            return Ok(response);
                                        }
                                        Err(fallback_err) => {
                                            error!("Fallback UDP handling also failed: {}", fallback_err);
                                        }
                                    }
                                }
                            }
                        }

                        return Err(e);
                    }
                }
            }
        }

        warn!("No active protocol handler available for UDP");
        Ok(Vec::new())
    }

    async fn connect_to_node(&self, node: &ProxyNode) -> Result<()> {
        info!("Connecting to node: {} ({})", node.name, node.address);

        let protocol_type = ProtocolType::from_string(&node.protocol);
        let handler_name = protocol_type.to_string();

        // Initialize the appropriate handler
        self.initialize_handler(&handler_name, node).await?;

        // Set as current handler
        self.set_current_handler(handler_name).await?;

        info!("Successfully connected to node: {}", node.name);
        Ok(())
    }

    async fn disconnect_from_node(&self) -> Result<()> {
        info!("Disconnecting from current node");

        if let Some(handler_name) = self.get_active_handler().await {
            self.close_handler(&handler_name).await?;

            // Clear current handler
            let mut current = self.current_handler.write().await;
            *current = None;

            info!("Disconnected from node");
        }

        Ok(())
    }

    async fn test_node_connectivity(&self, node: &ProxyNode) -> Result<bool> {
        info!("Testing connectivity to node: {}", node.name);

        let protocol_type = ProtocolType::from_string(&node.protocol);
        let handler_name = protocol_type.to_string();

        // Test with the appropriate handler
        match self.test_handler_connectivity(&handler_name).await {
            Ok(result) => {
                info!("Connectivity test for {}: {}", node.name, if result { "PASSED" } else { "FAILED" });
                Ok(result)
            }
            Err(e) => {
                warn!("Connectivity test error for {}: {}", node.name, e);
                Ok(false)
            }
        }
    }

    async fn get_or_create_tcp_connection(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
    ) -> Result<String> {
        debug!("Getting or creating TCP connection: {} -> {}", local_addr, remote_addr);

        // Configure TCP manager with current handler's proxy settings
        if let Some(handler_name) = self.get_active_handler().await {
            let handlers = self.handlers.read().await;
            if let Some(handler) = handlers.get(&handler_name) {
                if let Some(socks5_handler) = handler.as_any().downcast_ref::<Socks5Handler>() {
                    if let Some(node_config) = &socks5_handler.node_config {
                        let username = node_config.config.get("username").cloned();
                        let password = node_config.config.get("password").cloned();

                        // Configure the TCP manager with SOCKS5 proxy settings
                        self.tcp_manager.configure_proxy(node_config.address.clone(), username, password).await;
                    }
                }
            }
        }

        self.tcp_manager.get_or_create_connection(local_addr, remote_addr).await
    }

    async fn send_tcp_data(&self, connection_id: &str, data: Vec<u8>) -> Result<()> {
        debug!("Sending {} bytes to TCP connection: {}", data.len(), connection_id);
        self.tcp_manager.send_data(connection_id, data).await
    }

    async fn receive_tcp_data(&self, connection_id: &str) -> Result<Option<Vec<u8>>> {
        self.tcp_manager.receive_data(connection_id).await
    }

    async fn close_tcp_connection(&self, connection_id: &str) -> Result<()> {
        debug!("Closing TCP connection: {}", connection_id);
        self.tcp_manager.close_connection(connection_id).await
    }
}
