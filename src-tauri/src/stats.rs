use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH, Duration};
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize};

/// Traffic statistics for monitoring network usage
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TrafficStats {
    pub upload_speed: u64,      // bytes per second
    pub download_speed: u64,    // bytes per second
    pub total_upload: u64,      // total bytes uploaded
    pub total_download: u64,    // total bytes downloaded
    pub connection_count: u32,  // active connections
    pub last_updated: u64,      // timestamp
}

impl Default for TrafficStats {
    fn default() -> Self {
        Self {
            upload_speed: 0,
            download_speed: 0,
            total_upload: 0,
            total_download: 0,
            connection_count: 0,
            last_updated: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }
}

/// Traffic statistics tracker
pub struct TrafficStatsTracker {
    // Atomic counters for thread-safe updates
    total_upload: AtomicU64,
    total_download: AtomicU64,
    connection_count: AtomicU64,
    
    // Speed calculation data
    speed_data: Arc<RwLock<SpeedData>>,
}

#[derive(Debug)]
struct SpeedData {
    upload_samples: Vec<(u64, u64)>,    // (timestamp, bytes)
    download_samples: Vec<(u64, u64)>,  // (timestamp, bytes)
    last_upload_total: u64,
    last_download_total: u64,
    last_calculation: u64,
}

impl TrafficStatsTracker {
    pub fn new() -> Self {
        Self {
            total_upload: AtomicU64::new(0),
            total_download: AtomicU64::new(0),
            connection_count: AtomicU64::new(0),
            speed_data: Arc::new(RwLock::new(SpeedData {
                upload_samples: Vec::new(),
                download_samples: Vec::new(),
                last_upload_total: 0,
                last_download_total: 0,
                last_calculation: current_timestamp(),
            })),
        }
    }

    /// Record uploaded bytes
    pub fn record_upload(&self, bytes: u64) {
        self.total_upload.fetch_add(bytes, Ordering::Relaxed);
    }

    /// Record downloaded bytes
    pub fn record_download(&self, bytes: u64) {
        self.total_download.fetch_add(bytes, Ordering::Relaxed);
    }

    /// Increment connection count
    pub fn increment_connections(&self) {
        self.connection_count.fetch_add(1, Ordering::Relaxed);
    }

    /// Decrement connection count
    pub fn decrement_connections(&self) {
        self.connection_count.fetch_sub(1, Ordering::Relaxed);
    }

    /// Get current traffic statistics
    pub async fn get_stats(&self) -> TrafficStats {
        let total_upload = self.total_upload.load(Ordering::Relaxed);
        let total_download = self.total_download.load(Ordering::Relaxed);
        let connection_count = self.connection_count.load(Ordering::Relaxed) as u32;

        // Calculate speeds
        let (upload_speed, download_speed) = self.calculate_speeds(total_upload, total_download).await;

        TrafficStats {
            upload_speed,
            download_speed,
            total_upload,
            total_download,
            connection_count,
            last_updated: current_timestamp(),
        }
    }

    /// Calculate current upload and download speeds
    async fn calculate_speeds(&self, current_upload: u64, current_download: u64) -> (u64, u64) {
        let mut speed_data = self.speed_data.write().await;
        let now = current_timestamp();
        
        // Calculate time difference
        let time_diff = now.saturating_sub(speed_data.last_calculation);
        if time_diff == 0 {
            return (0, 0);
        }

        // Calculate upload speed
        let upload_diff = current_upload.saturating_sub(speed_data.last_upload_total);
        let upload_speed = if time_diff > 0 {
            upload_diff / time_diff
        } else {
            0
        };

        // Calculate download speed
        let download_diff = current_download.saturating_sub(speed_data.last_download_total);
        let download_speed = if time_diff > 0 {
            download_diff / time_diff
        } else {
            0
        };

        // Update samples for smoothing (keep last 10 samples)
        speed_data.upload_samples.push((now, upload_speed));
        speed_data.download_samples.push((now, download_speed));
        
        if speed_data.upload_samples.len() > 10 {
            speed_data.upload_samples.remove(0);
        }
        if speed_data.download_samples.len() > 10 {
            speed_data.download_samples.remove(0);
        }

        // Calculate smoothed speeds (average of recent samples)
        let smoothed_upload = if !speed_data.upload_samples.is_empty() {
            speed_data.upload_samples.iter().map(|(_, speed)| *speed).sum::<u64>() 
                / speed_data.upload_samples.len() as u64
        } else {
            upload_speed
        };

        let smoothed_download = if !speed_data.download_samples.is_empty() {
            speed_data.download_samples.iter().map(|(_, speed)| *speed).sum::<u64>() 
                / speed_data.download_samples.len() as u64
        } else {
            download_speed
        };

        // Update tracking data
        speed_data.last_upload_total = current_upload;
        speed_data.last_download_total = current_download;
        speed_data.last_calculation = now;

        (smoothed_upload, smoothed_download)
    }

    /// Reset all statistics
    pub async fn reset(&self) {
        self.total_upload.store(0, Ordering::Relaxed);
        self.total_download.store(0, Ordering::Relaxed);
        self.connection_count.store(0, Ordering::Relaxed);
        
        let mut speed_data = self.speed_data.write().await;
        speed_data.upload_samples.clear();
        speed_data.download_samples.clear();
        speed_data.last_upload_total = 0;
        speed_data.last_download_total = 0;
        speed_data.last_calculation = current_timestamp();
    }

    /// Start periodic speed calculation task
    pub fn start_speed_calculation_task(self: Arc<Self>) {
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(1));
            
            loop {
                interval.tick().await;
                
                // Trigger speed calculation by getting stats
                let _ = self.get_stats().await;
            }
        });
    }
}

/// Get current timestamp in seconds
fn current_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs()
}

/// Global traffic statistics instance
static mut GLOBAL_STATS: Option<Arc<TrafficStatsTracker>> = None;
static INIT: std::sync::Once = std::sync::Once::new();

/// Get global traffic statistics tracker
pub fn get_global_stats() -> Arc<TrafficStatsTracker> {
    unsafe {
        INIT.call_once(|| {
            let tracker = Arc::new(TrafficStatsTracker::new());
            // Start the speed calculation task
            Arc::clone(&tracker).start_speed_calculation_task();
            GLOBAL_STATS = Some(tracker);
        });
        
        GLOBAL_STATS.as_ref().unwrap().clone()
    }
}

/// Record traffic for global statistics
pub fn record_traffic(upload_bytes: u64, download_bytes: u64) {
    let stats = get_global_stats();
    if upload_bytes > 0 {
        stats.record_upload(upload_bytes);
    }
    if download_bytes > 0 {
        stats.record_download(download_bytes);
    }
}

/// Record new connection for global statistics
pub fn record_connection_start() {
    let stats = get_global_stats();
    stats.increment_connections();
}

/// Record connection end for global statistics
pub fn record_connection_end() {
    let stats = get_global_stats();
    stats.decrement_connections();
}
