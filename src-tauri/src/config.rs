use anyhow::{Result, anyhow};
use async_trait::async_trait;
use aes_gcm::{Aes128Gcm, Key, Nonce, aead::{Aead, KeyInit}};
use base64::{Engine as _, engine::general_purpose};
use reqwest::Client;
use tokio::sync::RwLock;
use tokio::time::{sleep, Duration, timeout};
use log::{info, warn, error, debug};
use serde_json;
use std::time::SystemTime;

use crate::traits::{ConfigManager, ProxyConfig, ProxyNode, ProxyRule};
use crate::rules::parse_rule_from_string;

/// Encrypted configuration manager
pub struct EncryptedConfigManager {
    /// HTTP client for downloading config
    client: Client,
    /// Current configuration
    current_config: RwLock<Option<ProxyConfig>>,
    /// Hardcoded encryption key (AES-128)
    encryption_key: [u8; 16],
    /// Hardcoded config URL
    config_url: String,
    /// Last successful fetch time
    last_fetch_time: RwLock<Option<SystemTime>>,
    /// Maximum retry attempts
    max_retries: u32,
    /// Request timeout in seconds
    request_timeout: u64,
}

impl EncryptedConfigManager {
    pub fn new() -> Self {
        // Hardcoded AES-128 key (in production, this should be more secure)
        let encryption_key = [
            0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
            0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c,
        ];

        // Hardcoded config URL (replace with actual URL)
        let config_url = "https://example.com/proxy-config.enc".to_string();

        Self {
            client: Client::builder()
                .timeout(Duration::from_secs(30))
                .build()
                .expect("Failed to create HTTP client"),
            current_config: RwLock::new(None),
            encryption_key,
            config_url,
            last_fetch_time: RwLock::new(None),
            max_retries: 3,
            request_timeout: 30,
        }
    }

    /// Decrypt encrypted configuration data
    fn decrypt_config(&self, encrypted_data: &[u8]) -> Result<Vec<u8>> {
        if encrypted_data.len() < 12 {
            return Err(anyhow!("Encrypted data too short"));
        }

        // Extract nonce (first 12 bytes) and ciphertext
        let (nonce_bytes, ciphertext) = encrypted_data.split_at(12);
        
        let key = Key::<Aes128Gcm>::from_slice(&self.encryption_key);
        let cipher = Aes128Gcm::new(key);
        let nonce = Nonce::from_slice(nonce_bytes);

        let plaintext = cipher.decrypt(nonce, ciphertext)
            .map_err(|e| anyhow!("Decryption failed: {}", e))?;

        Ok(plaintext)
    }

    /// Parse configuration from JSON
    fn parse_config(&self, json_data: &[u8]) -> Result<ProxyConfig> {
        let json_str = String::from_utf8(json_data.to_vec())
            .map_err(|e| anyhow!("Invalid UTF-8 in config: {}", e))?;

        // Parse as raw JSON first to handle custom format
        let raw_config: serde_json::Value = serde_json::from_str(&json_str)
            .map_err(|e| anyhow!("Invalid JSON in config: {}", e))?;

        // Extract nodes
        let nodes = self.parse_nodes(&raw_config)?;
        
        // Extract rules
        let rules = self.parse_rules(&raw_config)?;

        // Extract version
        let version = raw_config.get("version")
            .and_then(|v| v.as_str())
            .unwrap_or("unknown")
            .to_string();

        Ok(ProxyConfig {
            nodes,
            rules,
            version,
        })
    }

    /// Parse proxy nodes from config
    fn parse_nodes(&self, config: &serde_json::Value) -> Result<Vec<ProxyNode>> {
        let nodes_array = config.get("nodes")
            .and_then(|n| n.as_array())
            .ok_or_else(|| anyhow!("Missing or invalid 'nodes' field"))?;

        let mut nodes = Vec::new();
        
        for (index, node_value) in nodes_array.iter().enumerate() {
            let node_obj = node_value.as_object()
                .ok_or_else(|| anyhow!("Node {} is not an object", index))?;

            let id = node_obj.get("id")
                .and_then(|v| v.as_str())
                .unwrap_or(&index.to_string())
                .to_string();

            let name = node_obj.get("name")
                .and_then(|v| v.as_str())
                .ok_or_else(|| anyhow!("Node {} missing name", index))?
                .to_string();

            let address = node_obj.get("address")
                .and_then(|v| v.as_str())
                .ok_or_else(|| anyhow!("Node {} missing address", index))?
                .to_string();

            let protocol = node_obj.get("protocol")
                .and_then(|v| v.as_str())
                .unwrap_or("custom")
                .to_string();

            // Parse additional config parameters
            let mut config_map = std::collections::HashMap::new();
            if let Some(config_obj) = node_obj.get("config").and_then(|v| v.as_object()) {
                for (key, value) in config_obj {
                    if let Some(str_value) = value.as_str() {
                        config_map.insert(key.clone(), str_value.to_string());
                    }
                }
            }

            nodes.push(ProxyNode {
                id,
                name,
                address,
                protocol,
                config: config_map,
            });
        }

        Ok(nodes)
    }

    /// Parse proxy rules from config
    fn parse_rules(&self, config: &serde_json::Value) -> Result<Vec<ProxyRule>> {
        let empty_vec = Vec::new();
        let rules_array = config.get("rules")
            .and_then(|r| r.as_array())
            .unwrap_or(&empty_vec);

        let mut rules = Vec::new();
        
        for (index, rule_value) in rules_array.iter().enumerate() {
            if let Some(rule_str) = rule_value.as_str() {
                match parse_rule_from_string(rule_str) {
                    Ok(rule) => rules.push(rule),
                    Err(e) => {
                        warn!("Failed to parse rule {}: {} - {}", index, rule_str, e);
                    }
                }
            } else {
                warn!("Rule {} is not a string", index);
            }
        }

        Ok(rules)
    }

    /// Create a test configuration for development
    pub fn create_test_config() -> ProxyConfig {
        use std::collections::HashMap;

        let mut test_node_config = HashMap::new();
        test_node_config.insert("username".to_string(), "lunyduo".to_string());
        test_node_config.insert("password".to_string(), "lunyduo888".to_string());

        ProxyConfig {
            nodes: vec![
                ProxyNode {
                    id: "1".to_string(),
                    name: "Test SOCKS5 Server".to_string(),
                    address: "**************:8888".to_string(),
                    protocol: "socks5".to_string(),
                    config: test_node_config,
                },
            ],
            rules: vec![
                crate::traits::ProxyRule::DomainSuffix {
                    suffix: "google.com".to_string(),
                    node_id: "1".to_string(),
                },
                crate::traits::ProxyRule::DomainKeyword {
                    keyword: "github".to_string(),
                    node_id: "1".to_string(),
                },
                crate::traits::ProxyRule::GeoSite {
                    site: "google".to_string(),
                    node_id: "1".to_string(),
                },
            ],
            version: "test-1.0".to_string(),
        }
    }

    /// Download configuration with retry mechanism
    async fn fetch_config_with_retries(&self) -> Result<ProxyConfig> {
        let mut last_error = None;

        for attempt in 1..=self.max_retries {
            info!("Config download attempt {} of {}", attempt, self.max_retries);

            match self.download_and_process_config().await {
                Ok(config) => {
                    info!("Successfully downloaded config on attempt {}", attempt);
                    return Ok(config);
                }
                Err(e) => {
                    error!("Config download attempt {} failed: {}", attempt, e);
                    last_error = Some(e);

                    if attempt < self.max_retries {
                        let delay = Duration::from_secs(2_u64.pow(attempt - 1)); // Exponential backoff
                        info!("Retrying in {:?}...", delay);
                        sleep(delay).await;
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| anyhow!("All config download attempts failed")))
    }

    /// Download and process configuration (single attempt)
    async fn download_and_process_config(&self) -> Result<ProxyConfig> {
        // Download encrypted config with timeout
        let response = timeout(
            Duration::from_secs(self.request_timeout),
            self.client.get(&self.config_url).send()
        ).await
        .map_err(|_| anyhow!("Config download timed out after {} seconds", self.request_timeout))?
        .map_err(|e| anyhow!("Failed to download config: {}", e))?;

        if !response.status().is_success() {
            return Err(anyhow!("Config download failed with status: {}", response.status()));
        }

        let encrypted_data = response.bytes().await
            .map_err(|e| anyhow!("Failed to read config data: {}", e))?;

        // Decode base64 if needed
        let encrypted_bytes = if encrypted_data.starts_with(b"data:") ||
                                 encrypted_data.iter().all(|&b| b.is_ascii()) {
            general_purpose::STANDARD.decode(&encrypted_data)
                .map_err(|e| anyhow!("Failed to decode base64 config: {}", e))?
        } else {
            encrypted_data.to_vec()
        };

        // Decrypt config
        let decrypted_data = self.decrypt_config(&encrypted_bytes)?;

        // Parse config
        let config = self.parse_config(&decrypted_data)?;

        // Validate config
        if !self.validate_config(&config).await? {
            return Err(anyhow!("Configuration validation failed"));
        }

        Ok(config)
    }

    /// Check if configuration needs to be refreshed
    pub async fn should_refresh_config(&self) -> bool {
        let last_fetch = self.last_fetch_time.read().await;
        match *last_fetch {
            Some(time) => {
                // Refresh every 24 hours
                time.elapsed().unwrap_or(Duration::from_secs(0)) > Duration::from_secs(24 * 60 * 60)
            }
            None => true, // Never fetched before
        }
    }

    /// Get time since last successful config fetch
    pub async fn time_since_last_fetch(&self) -> Option<Duration> {
        let last_fetch = self.last_fetch_time.read().await;
        last_fetch.and_then(|time| time.elapsed().ok())
    }
}

#[async_trait]
impl ConfigManager for EncryptedConfigManager {
    /// Download and decrypt configuration from server
    async fn fetch_config(&self) -> Result<ProxyConfig> {
        info!("Fetching configuration from {}", self.config_url);

        // For development, return test config if URL is not reachable
        if self.config_url.contains("example.com") {
            warn!("Using test configuration (development mode)");
            let test_config = Self::create_test_config();

            // Store the test config and update fetch time
            {
                let mut current = self.current_config.write().await;
                *current = Some(test_config.clone());
                let mut last_fetch = self.last_fetch_time.write().await;
                *last_fetch = Some(SystemTime::now());
            }

            return Ok(test_config);
        }

        // Try to download with retries
        let config = self.fetch_config_with_retries().await?;

        // Store current config and update fetch time
        {
            let mut current = self.current_config.write().await;
            *current = Some(config.clone());
            let mut last_fetch = self.last_fetch_time.write().await;
            *last_fetch = Some(SystemTime::now());
        }

        info!("Successfully loaded configuration with {} nodes and {} rules",
              config.nodes.len(), config.rules.len());

        Ok(config)
    }



    /// Get the current configuration
    async fn get_current_config(&self) -> Result<Option<ProxyConfig>> {
        let current = self.current_config.read().await;
        Ok(current.clone())
    }

    /// Validate configuration integrity
    async fn validate_config(&self, config: &ProxyConfig) -> Result<bool> {
        // Check if we have at least one node
        if config.nodes.is_empty() {
            error!("Configuration has no proxy nodes");
            return Ok(false);
        }

        // Validate each node
        for node in &config.nodes {
            if node.name.is_empty() || node.address.is_empty() {
                error!("Invalid node configuration: {}", node.id);
                return Ok(false);
            }

            // Basic address format validation
            if !node.address.contains(':') {
                error!("Node {} has invalid address format: {}", node.id, node.address);
                return Ok(false);
            }
        }

        // Validate rules reference existing nodes
        let node_ids: std::collections::HashSet<String> = config.nodes.iter()
            .map(|n| n.id.clone())
            .collect();

        for rule in &config.rules {
            let rule_node_id = match rule {
                crate::traits::ProxyRule::Domain { node_id, .. } |
                crate::traits::ProxyRule::DomainSuffix { node_id, .. } |
                crate::traits::ProxyRule::DomainKeyword { node_id, .. } |
                crate::traits::ProxyRule::IpCidr { node_id, .. } |
                crate::traits::ProxyRule::GeoIp { node_id, .. } |
                crate::traits::ProxyRule::GeoSite { node_id, .. } => node_id,
            };

            if !node_ids.contains(rule_node_id) {
                warn!("Rule references non-existent node: {}", rule_node_id);
            }
        }

        debug!("Configuration validation passed");
        Ok(true)
    }

    /// Get encryption key (hardcoded in implementation)
    fn get_encryption_key(&self) -> &[u8] {
        &self.encryption_key
    }

    /// Get config download URL (hardcoded in implementation)
    fn get_config_url(&self) -> &str {
        &self.config_url
    }
}
