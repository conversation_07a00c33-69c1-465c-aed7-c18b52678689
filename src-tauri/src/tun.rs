use anyhow::{Result, anyhow};
use std::net::{IpAddr, Ipv4Addr};
use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use log::{info, warn, error, debug};
use tun_easytier::{Configuration, create_as_async, AsyncDevice};
use privilege::user;

use crate::traits::{DnsManager, TrafficRouter, BackendProxy, ProxyMode, RouteManager};
use crate::packet::PacketProcessor;
use crate::stats::{record_traffic, record_connection_start, record_connection_end};
use crate::route::SystemRouteManager;

/// TUN/TAP interface manager
pub struct TunManager {
    device: Option<Arc<AsyncDevice>>,
    packet_processor: Arc<PacketProcessor>,
    dns_manager: Arc<dyn DnsManager>,
    traffic_router: Arc<dyn TrafficRouter>,
    backend_proxy: Arc<dyn BackendProxy>,
    route_manager: Arc<SystemRouteManager>,
    is_running: Arc<RwLock<bool>>,
    tun_ip: Ipv4Addr,
    tun_netmask: Ipv4Addr,
    interface_name: String,
    proxy_server_ips: Arc<RwLock<Vec<IpAddr>>>, // IPs to exclude from TUN routing
}

impl TunManager {
    pub fn new(
        dns_manager: Arc<dyn DnsManager>,
        traffic_router: Arc<dyn TrafficRouter>,
        backend_proxy: Arc<dyn BackendProxy>,
    ) -> Self {
        Self {
            device: None,
            packet_processor: Arc::new(PacketProcessor::new()),
            dns_manager,
            traffic_router,
            backend_proxy,
            route_manager: Arc::new(SystemRouteManager::new()),
            is_running: Arc::new(RwLock::new(false)),
            tun_ip: Ipv4Addr::new(10, 0, 0, 1),
            tun_netmask: Ipv4Addr::new(255, 255, 255, 0),
            interface_name: "tun-proxy".to_string(),
            proxy_server_ips: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Start the TUN interface
    pub async fn start(&mut self) -> Result<()> {
        if *self.is_running.read().await {
            return Ok(());
        }

        // Check for admin privileges before attempting to create TUN interface
        if !user::privileged() {
            error!("Admin privileges required to create TUN interface");
            return Err(anyhow!(
                "Admin privileges required. Please restart the application as administrator/sudo."
            ));
        }

        info!("Starting TUN interface...");

        // Create TUN device configuration
        let mut config = Configuration::default();
        config.tun_name(&self.interface_name)
              .address(self.tun_ip)
              .netmask(self.tun_netmask)
              .mtu(1500)
              .up();

        // Create TUN device
        match create_as_async(&config) {
            Ok(device) => {
                info!("TUN device '{}' created successfully", self.interface_name);
                self.device = Some(Arc::new(device));
                *self.is_running.write().await = true;

                // Set up system routes to direct traffic through TUN interface
                self.setup_routes().await?;

                // Start packet processing loop
                self.start_packet_loop().await?;

                info!("TUN interface started successfully");
                Ok(())
            }
            Err(e) => {
                error!("Failed to create TUN device: {}", e);
                Err(anyhow!("Failed to create TUN device: {}", e))
            }
        }
    }

    /// Stop the TUN interface
    pub async fn stop(&mut self) -> Result<()> {
        if !*self.is_running.read().await {
            return Ok(());
        }

        info!("Stopping TUN interface...");
        *self.is_running.write().await = false;

        // Restore original routes
        self.cleanup_routes().await?;

        // Close TUN device
        if let Some(device) = self.device.take() {
            drop(device);
            info!("TUN device closed");
        }

        info!("TUN interface stopped");
        Ok(())
    }

    /// Start the packet processing loop
    async fn start_packet_loop(&mut self) -> Result<()> {
        let device = self.device.as_ref()
            .ok_or_else(|| anyhow!("TUN device not initialized"))?;

        let (tx, mut rx) = mpsc::channel::<Vec<u8>>(1000);
        let is_running = Arc::clone(&self.is_running);
        let dns_manager = Arc::clone(&self.dns_manager);
        let traffic_router = Arc::clone(&self.traffic_router);
        let backend_proxy = Arc::clone(&self.backend_proxy);
        let packet_processor = Arc::clone(&self.packet_processor);

        // Clone device for both tasks
        let device_read = Arc::clone(device);
        let device_write = Arc::clone(device);

        let tx_clone = tx.clone();
        let is_running_clone = Arc::clone(&is_running);

        // Spawn packet reading task
        tokio::spawn(async move {
            let mut buffer = vec![0u8; 2048];

            while *is_running_clone.read().await {
                match device_read.recv(&mut buffer).await {
                    Ok(size) => {
                        if size > 0 {
                            let packet = buffer[..size].to_vec();
                            if let Err(e) = tx_clone.send(packet).await {
                                error!("Failed to send packet to processor: {}", e);
                                break;
                            }
                        }
                    }
                    Err(e) => {
                        error!("Failed to read from TUN device: {}", e);
                        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    }
                }
            }
            debug!("Packet reading task stopped");
        });

        // Clone proxy server IPs for loop prevention
        let proxy_server_ips = Arc::clone(&self.proxy_server_ips);

        // Spawn packet processing task
        let packet_processor_cleanup = Arc::clone(&packet_processor);
        tokio::spawn(async move {
            while let Some(packet) = rx.recv().await {
                if let Err(e) = Self::process_packet(
                    packet,
                    &device_write,
                    &packet_processor,
                    &dns_manager,
                    &traffic_router,
                    &backend_proxy,
                    &proxy_server_ips,
                ).await {
                    error!("Failed to process packet: {}", e);
                }
            }
            debug!("Packet processing task stopped");
        });

        // Spawn cleanup task for TCP connections and fragments
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60)); // Clean up every minute

            loop {
                interval.tick().await;
                packet_processor_cleanup.cleanup_old_connections().await;
            }
        });

        info!("Packet processing loop started");
        Ok(())
    }

    /// Process a single packet
    async fn process_packet(
        packet: Vec<u8>,
        device: &Arc<AsyncDevice>,
        packet_processor: &PacketProcessor,
        dns_manager: &Arc<dyn DnsManager>,
        traffic_router: &Arc<dyn TrafficRouter>,
        backend_proxy: &Arc<dyn BackendProxy>,
        proxy_server_ips: &Arc<RwLock<Vec<IpAddr>>>,
    ) -> Result<()> {
        // Parse the packet
        let parsed = packet_processor.parse_packet(&packet)?;

        match parsed {
            crate::packet::ParsedPacket::Tcp {
                src_addr,
                dst_addr,
                src_port,
                dst_port,
                flags,
                seq_num,
                ack_num,
                payload
            } => {
                debug!("TCP packet: {}:{} -> {}:{}", src_addr, src_port, dst_addr, dst_port);

                // Check for traffic loop prevention
                if Self::should_exclude_from_tun_static(dst_addr, proxy_server_ips).await {
                    debug!("Excluding TCP traffic to {} (proxy server or local network)", dst_addr);
                    return Ok(());
                }

                // Handle TCP connection state
                let connection_key = format!("{}:{}->{}:{}", src_addr, src_port, dst_addr, dst_port);

                // Record traffic statistics
                record_traffic(0, packet.len() as u64); // Incoming packet

                // Track TCP connection state
                let _connection = packet_processor.track_tcp_connection(
                    &connection_key,
                    seq_num,
                    ack_num,
                    flags
                ).await?;

                // Determine routing for this connection
                let dest_domain = dns_manager.get_domain_for_fake_ip(dst_addr).await?;
                let route_target = if let Some(domain) = dest_domain {
                    traffic_router.route_request(&domain, Some(dst_addr), ProxyMode::Rules).await?
                } else {
                    traffic_router.route_request(&dst_addr.to_string(), Some(dst_addr), ProxyMode::Rules).await?
                };

                if let Some(_node_id) = route_target {
                    // Handle TCP connection with persistent connection management
                    Self::handle_tcp_connection(
                        &connection_key,
                        std::net::SocketAddr::new(src_addr, src_port),
                        std::net::SocketAddr::new(dst_addr, dst_port),
                        flags,
                        seq_num,
                        ack_num,
                        payload,
                        device,
                        packet_processor,
                        backend_proxy,
                    ).await?;
                } else {
                    // Direct connection - let it pass through
                    debug!("Allowing direct connection to {}", dst_addr);
                }

                // Check if this is a FIN packet (connection closing)
                if flags & 0x01 != 0 { // FIN flag
                    record_connection_end();
                }
            }

            crate::packet::ParsedPacket::Udp {
                src_addr,
                dst_addr,
                src_port,
                dst_port,
                payload
            } => {
                debug!("UDP packet: {}:{} -> {}:{}", src_addr, src_port, dst_addr, dst_port);

                // Check for traffic loop prevention (except for DNS queries)
                if dst_port != 53 && Self::should_exclude_from_tun_static(dst_addr, proxy_server_ips).await {
                    debug!("Excluding UDP traffic to {} (proxy server or local network)", dst_addr);
                    return Ok(());
                }

                // Record traffic statistics
                record_traffic(0, packet.len() as u64); // Incoming packet

                // Check if this is a DNS query (port 53)
                if dst_port == 53 {
                    let dns_response = dns_manager.handle_dns_query(&payload).await?;
                    let response_packet = packet_processor.build_udp_response(
                        dst_addr, dst_port, src_addr, src_port, &dns_response
                    )?;
                    record_traffic(response_packet.len() as u64, 0); // Outgoing response
                    if let Err(e) = device.send(&response_packet).await {
                        warn!("Failed to send DNS response: {}", e);
                    }
                } else {
                    // Handle regular UDP traffic
                    let dest_domain = dns_manager.get_domain_for_fake_ip(dst_addr).await?;
                    let route_target = if let Some(domain) = dest_domain {
                        traffic_router.route_request(&domain, Some(dst_addr), ProxyMode::Rules).await?
                    } else {
                        traffic_router.route_request(&dst_addr.to_string(), Some(dst_addr), ProxyMode::Rules).await?
                    };

                    if let Some(_node_id) = route_target {
                        // Route through proxy
                        let response = backend_proxy.handle_udp_packet(
                            std::net::SocketAddr::new(src_addr, src_port),
                            std::net::SocketAddr::new(dst_addr, dst_port),
                            payload,
                        ).await?;

                        // Send response back through TUN
                        if !response.is_empty() {
                            let response_packet = packet_processor.build_udp_response(
                                dst_addr, dst_port, src_addr, src_port, &response
                            )?;
                            record_traffic(response_packet.len() as u64, 0); // Outgoing response
                            if let Err(e) = device.send(&response_packet).await {
                                warn!("Failed to send UDP response: {}", e);
                            }
                        }
                    }
                }
            }

            crate::packet::ParsedPacket::Other => {
                debug!("Ignoring non-TCP/UDP packet");
            }
        }

        Ok(())
    }

    /// Handle TCP connection with persistent connection management
    async fn handle_tcp_connection(
        connection_key: &str,
        local_addr: std::net::SocketAddr,
        remote_addr: std::net::SocketAddr,
        flags: u8,
        seq_num: u32,
        ack_num: u32,
        payload: Vec<u8>,
        device: &Arc<AsyncDevice>,
        packet_processor: &PacketProcessor,
        backend_proxy: &Arc<dyn BackendProxy>,
    ) -> Result<()> {
        // Check if this is a SYN packet (new connection)
        if flags & 0x02 != 0 { // SYN flag
            debug!("New TCP connection: {}", connection_key);
            record_connection_start();

            // Create or get persistent connection
            let connection_id = backend_proxy.get_or_create_tcp_connection(local_addr, remote_addr).await?;
            debug!("TCP connection established with ID: {}", connection_id);

            // Send SYN-ACK response to complete handshake
            let syn_ack_packet = packet_processor.build_tcp_response(
                connection_key,
                remote_addr.ip(), remote_addr.port(),
                local_addr.ip(), local_addr.port(),
                &[],
                0x12 // SYN + ACK flags
            ).await?;

            if let Err(e) = device.send(&syn_ack_packet).await {
                warn!("Failed to send SYN-ACK: {}", e);
            }

            // Start bidirectional forwarding task for this connection
            Self::start_tcp_forwarding_task(
                connection_id,
                connection_key.to_string(),
                local_addr,
                remote_addr,
                Arc::clone(device),
                Arc::clone(packet_processor),
                Arc::clone(backend_proxy),
            ).await;

        } else if flags & 0x01 != 0 { // FIN flag
            debug!("TCP connection closing: {}", connection_key);
            record_connection_end();

            // Close the persistent connection
            let connection_id = format!("{}->{}", local_addr, remote_addr);
            if let Err(e) = backend_proxy.close_tcp_connection(&connection_id).await {
                warn!("Failed to close TCP connection {}: {}", connection_id, e);
            }

        } else if !payload.is_empty() {
            // Data packet - forward through persistent connection
            let connection_id = format!("{}->{}", local_addr, remote_addr);

            if let Err(e) = backend_proxy.send_tcp_data(&connection_id, payload).await {
                warn!("Failed to send TCP data for connection {}: {}", connection_id, e);
            }
        }

        Ok(())
    }

    /// Start a bidirectional TCP forwarding task
    async fn start_tcp_forwarding_task(
        connection_id: String,
        connection_key: String,
        local_addr: std::net::SocketAddr,
        remote_addr: std::net::SocketAddr,
        device: Arc<AsyncDevice>,
        packet_processor: Arc<PacketProcessor>,
        backend_proxy: Arc<dyn BackendProxy>,
    ) {
        tokio::spawn(async move {
            debug!("Starting TCP forwarding task for connection: {}", connection_id);

            let mut interval = tokio::time::interval(tokio::time::Duration::from_millis(10));

            loop {
                interval.tick().await;

                // Check for data from the proxy connection
                match backend_proxy.receive_tcp_data(&connection_id).await {
                    Ok(Some(data)) => {
                        debug!("Received {} bytes from proxy for connection {}", data.len(), connection_id);

                        // Build TCP packet and send back through TUN
                        match packet_processor.build_tcp_response(
                            &connection_key,
                            remote_addr.ip(), remote_addr.port(),
                            local_addr.ip(), local_addr.port(),
                            &data,
                            0x18 // PSH + ACK flags
                        ).await {
                            Ok(response_packet) => {
                                record_traffic(response_packet.len() as u64, 0);
                                if let Err(e) = device.send(&response_packet).await {
                                    warn!("Failed to send TCP response: {}", e);
                                    break;
                                }
                            }
                            Err(e) => {
                                error!("Failed to build TCP response packet: {}", e);
                                break;
                            }
                        }
                    }
                    Ok(None) => {
                        // No data available, continue polling
                    }
                    Err(e) => {
                        debug!("TCP connection {} closed or error: {}", connection_id, e);
                        break;
                    }
                }
            }

            debug!("TCP forwarding task ended for connection: {}", connection_id);
        });
    }

    /// Get TUN interface IP address
    pub fn get_tun_ip(&self) -> IpAddr {
        IpAddr::V4(self.tun_ip)
    }

    /// Check if TUN interface is running
    pub async fn is_running(&self) -> bool {
        *self.is_running.read().await
    }

    /// Add proxy server IP to exclusion list (to prevent routing loops)
    pub async fn add_proxy_server_ip(&self, ip: IpAddr) -> Result<()> {
        let mut proxy_ips = self.proxy_server_ips.write().await;
        if !proxy_ips.contains(&ip) {
            proxy_ips.push(ip);
            info!("Added proxy server IP to exclusion list: {}", ip);
        }
        Ok(())
    }

    /// Remove proxy server IP from exclusion list
    pub async fn remove_proxy_server_ip(&self, ip: IpAddr) -> Result<()> {
        let mut proxy_ips = self.proxy_server_ips.write().await;
        proxy_ips.retain(|&x| x != ip);
        info!("Removed proxy server IP from exclusion list: {}", ip);
        Ok(())
    }

    /// Check if an IP should be excluded from TUN routing (to prevent loops)
    async fn should_exclude_from_tun(&self, ip: IpAddr) -> bool {
        Self::should_exclude_from_tun_static(ip, &self.proxy_server_ips).await
    }

    /// Static version of should_exclude_from_tun for use in packet processing
    async fn should_exclude_from_tun_static(
        ip: IpAddr,
        proxy_server_ips: &Arc<RwLock<Vec<IpAddr>>>
    ) -> bool {
        // Exclude proxy server IPs
        let proxy_ips = proxy_server_ips.read().await;
        if proxy_ips.contains(&ip) {
            return true;
        }

        // Exclude local network ranges
        match ip {
            IpAddr::V4(ipv4) => {
                // Exclude localhost, private networks, and TUN network
                ipv4.is_loopback() ||
                ipv4.is_private() ||
                (ipv4.octets()[0] == 10 && ipv4.octets()[1] == 0 && ipv4.octets()[2] == 0)
            }
            IpAddr::V6(ipv6) => {
                // Exclude localhost and link-local
                ipv6.is_loopback() ||
                (ipv6.segments()[0] & 0xffc0) == 0xfe80
            }
        }
    }

    /// Set up system routes to direct traffic through TUN interface
    async fn setup_routes(&self) -> Result<()> {
        info!("Setting up system routes for TUN interface");

        // Set default route through TUN interface for global mode
        // This will route all traffic except excluded IPs through the TUN
        let tun_ip = IpAddr::V4(self.tun_ip);

        if let Err(e) = self.route_manager.set_default_route(tun_ip).await {
            warn!("Failed to set default route: {}. Traffic routing may not work properly.", e);
        }

        info!("System routes configured successfully");
        Ok(())
    }

    /// Clean up system routes when stopping TUN interface
    async fn cleanup_routes(&self) -> Result<()> {
        info!("Cleaning up system routes");

        // Restore original default route
        if let Err(e) = self.route_manager.restore_default_route().await {
            warn!("Failed to restore default route: {}", e);
        }

        info!("System routes cleaned up successfully");
        Ok(())
    }
}
