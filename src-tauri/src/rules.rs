use anyhow::{Result, anyhow};
use std::net::IpAddr;
use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;
use async_trait::async_trait;
use ipnet::IpNet;
use log::{debug, info, warn};

use crate::traits::{TrafficRouter, ProxyRule, ProxyMode};
use crate::geoip::GeoManager;

/// Traffic router implementation with rule matching
pub struct RuleBasedTrafficRouter {
    /// Current proxy rules
    rules: RwLock<Vec<ProxyRule>>,
    /// Fallback node for rule mode
    fallback_node: RwLock<Option<String>>,
    /// Enhanced GeoIP and GeoSite manager
    geo_manager: Arc<GeoManager>,
}

impl RuleBasedTrafficRouter {
    pub fn new() -> Self {
        let geo_manager = Arc::new(GeoManager::new());
        Self {
            rules: RwLock::new(Vec::new()),
            fallback_node: RwLock::new(None),
            geo_manager,
        }
    }

    pub async fn initialize(&self) -> Result<()> {
        self.geo_manager.initialize().await?;
        info!("RuleBasedTrafficRouter initialized with GeoIP/GeoSite support");
        Ok(())
    }

    /// Initialize common GeoSite mappings
    fn init_geosite_mappings() -> HashMap<String, Vec<String>> {
        let mut mappings = HashMap::new();
        
        // Google services
        mappings.insert("google".to_string(), vec![
            "google.com".to_string(),
            "googleapis.com".to_string(),
            "googleusercontent.com".to_string(),
            "googlevideo.com".to_string(),
            "gstatic.com".to_string(),
            "youtube.com".to_string(),
            "ytimg.com".to_string(),
        ]);
        
        // Facebook services
        mappings.insert("facebook".to_string(), vec![
            "facebook.com".to_string(),
            "fbcdn.net".to_string(),
            "instagram.com".to_string(),
            "whatsapp.com".to_string(),
        ]);
        
        // Microsoft services
        mappings.insert("microsoft".to_string(), vec![
            "microsoft.com".to_string(),
            "office.com".to_string(),
            "outlook.com".to_string(),
            "live.com".to_string(),
            "xbox.com".to_string(),
        ]);
        
        // Apple services
        mappings.insert("apple".to_string(), vec![
            "apple.com".to_string(),
            "icloud.com".to_string(),
            "itunes.com".to_string(),
            "appstore.com".to_string(),
        ]);
        
        mappings
    }

    /// Check if domain matches a domain suffix rule
    fn match_domain_suffix(&self, domain: &str, suffix: &str) -> bool {
        domain.ends_with(suffix) && (domain == suffix || domain.ends_with(&format!(".{}", suffix)))
    }

    /// Check if domain matches a domain keyword rule
    fn match_domain_keyword(&self, domain: &str, keyword: &str) -> bool {
        domain.contains(keyword)
    }

    /// Check if IP matches CIDR rule
    fn match_ip_cidr(&self, ip: IpAddr, cidr: &str) -> Result<bool> {
        let network: IpNet = cidr.parse()
            .map_err(|e| anyhow!("Invalid CIDR format {}: {}", cidr, e))?;
        Ok(network.contains(&ip))
    }

    /// Get country code for IP using GeoManager
    async fn get_country_for_ip(&self, ip: IpAddr) -> Result<Option<String>> {
        match self.geo_manager.get_country_for_ip(ip).await? {
            Some(entry) => Ok(Some(entry.country_code)),
            None => Ok(None),
        }
    }

    /// Check if domain belongs to a GeoSite category using GeoManager
    async fn match_geosite(&self, domain: &str, site: &str) -> bool {
        match self.geo_manager.match_geosite(domain, site).await {
            Ok(result) => result,
            Err(e) => {
                debug!("GeoSite matching error for {}:{}: {}", domain, site, e);
                false
            }
        }
    }


}

#[async_trait]
impl TrafficRouter for RuleBasedTrafficRouter {
    /// Determine which proxy node should handle a request based on rules
    async fn route_request(
        &self,
        destination: &str,
        dest_ip: Option<IpAddr>,
        mode: ProxyMode,
    ) -> Result<Option<String>> {
        match mode {
            ProxyMode::Direct => {
                debug!("Direct mode: allowing direct connection to {}", destination);
                Ok(None)
            }
            ProxyMode::Global => {
                debug!("Global mode: routing {} through fallback node", destination);
                Ok(self.get_fallback_node().await?)
            }
            ProxyMode::Rules => {
                debug!("Rule mode: checking rules for {}", destination);
                
                // First try domain-based rules
                if let Some(node_id) = self.match_domain_rules(destination).await? {
                    debug!("Domain rule matched for {}: node {}", destination, node_id);
                    return Ok(Some(node_id));
                }
                
                // Then try IP-based rules if IP is available
                if let Some(ip) = dest_ip {
                    if let Some(node_id) = self.match_ip_rules(ip).await? {
                        debug!("IP rule matched for {}: node {}", ip, node_id);
                        return Ok(Some(node_id));
                    }
                }
                
                // No rules matched, use fallback
                debug!("No rules matched for {}, using fallback", destination);
                Ok(self.get_fallback_node().await?)
            }
        }
    }

    /// Check if a domain matches any rules
    async fn match_domain_rules(&self, domain: &str) -> Result<Option<String>> {
        let rules = self.rules.read().await;
        
        for rule in rules.iter() {
            match rule {
                ProxyRule::Domain { domain: rule_domain, node_id } => {
                    if domain == rule_domain {
                        return Ok(Some(node_id.clone()));
                    }
                }
                ProxyRule::DomainSuffix { suffix, node_id } => {
                    if self.match_domain_suffix(domain, suffix) {
                        return Ok(Some(node_id.clone()));
                    }
                }
                ProxyRule::DomainKeyword { keyword, node_id } => {
                    if self.match_domain_keyword(domain, keyword) {
                        return Ok(Some(node_id.clone()));
                    }
                }
                ProxyRule::GeoSite { site, node_id } => {
                    if self.match_geosite(domain, site).await {
                        return Ok(Some(node_id.clone()));
                    }
                }
                _ => {} // Skip non-domain rules
            }
        }
        
        Ok(None)
    }

    /// Check if an IP matches any rules
    async fn match_ip_rules(&self, ip: IpAddr) -> Result<Option<String>> {
        let rules = self.rules.read().await;
        
        for rule in rules.iter() {
            match rule {
                ProxyRule::IpCidr { cidr, node_id } => {
                    if self.match_ip_cidr(ip, cidr)? {
                        return Ok(Some(node_id.clone()));
                    }
                }
                ProxyRule::GeoIp { country, node_id } => {
                    if let Some(ip_country) = self.get_country_for_ip(ip).await? {
                        if ip_country == *country {
                            return Ok(Some(node_id.clone()));
                        }
                    }
                }
                _ => {} // Skip non-IP rules
            }
        }
        
        Ok(None)
    }

    /// Get the fallback node for rule mode
    async fn get_fallback_node(&self) -> Result<Option<String>> {
        let fallback = self.fallback_node.read().await;
        Ok(fallback.clone())
    }

    /// Update routing rules
    async fn update_rules(&self, rules: Vec<ProxyRule>) -> Result<()> {
        let mut current_rules = self.rules.write().await;
        *current_rules = rules;
        info!("Updated {} routing rules", current_rules.len());
        Ok(())
    }

    /// Set the fallback node for rule mode
    async fn set_fallback_node(&self, node_id: Option<String>) -> Result<()> {
        let mut fallback = self.fallback_node.write().await;
        *fallback = node_id;
        Ok(())
    }
}

/// Parse rule from string format: TYPE,VALUE,NODE_ID
pub fn parse_rule_from_string(rule_str: &str) -> Result<ProxyRule> {
    let parts: Vec<&str> = rule_str.split(',').collect();
    if parts.len() != 3 {
        return Err(anyhow!("Invalid rule format: {}", rule_str));
    }

    let rule_type = parts[0];
    let value = parts[1].to_string();
    let node_id = parts[2].to_string();

    match rule_type {
        "DOMAIN" => Ok(ProxyRule::Domain { domain: value, node_id }),
        "DOMAIN-SUFFIX" => Ok(ProxyRule::DomainSuffix { suffix: value, node_id }),
        "DOMAIN-KEYWORD" => Ok(ProxyRule::DomainKeyword { keyword: value, node_id }),
        "IP-CIDR" => Ok(ProxyRule::IpCidr { cidr: value, node_id }),
        "GEOIP" => Ok(ProxyRule::GeoIp { country: value, node_id }),
        "GEOSITE" => Ok(ProxyRule::GeoSite { site: value, node_id }),
        _ => Err(anyhow!("Unknown rule type: {}", rule_type)),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::Ipv4Addr;

    #[tokio::test]
    async fn test_domain_suffix_matching() {
        let router = RuleBasedTrafficRouter::new();
        
        assert!(router.match_domain_suffix("google.com", "google.com"));
        assert!(router.match_domain_suffix("www.google.com", "google.com"));
        assert!(router.match_domain_suffix("mail.google.com", "google.com"));
        assert!(!router.match_domain_suffix("googlex.com", "google.com"));
        assert!(!router.match_domain_suffix("notgoogle.com", "google.com"));
    }

    #[tokio::test]
    async fn test_rule_parsing() {
        let rule = parse_rule_from_string("DOMAIN-SUFFIX,google.com,1").unwrap();
        match rule {
            ProxyRule::DomainSuffix { suffix, node_id } => {
                assert_eq!(suffix, "google.com");
                assert_eq!(node_id, "1");
            }
            _ => panic!("Wrong rule type"),
        }
    }

    #[tokio::test]
    async fn test_ip_cidr_matching() {
        let router = RuleBasedTrafficRouter::new();
        let ip = IpAddr::V4(Ipv4Addr::new(192, 168, 1, 100));
        
        assert!(router.match_ip_cidr(ip, "***********/24").unwrap());
        assert!(!router.match_ip_cidr(ip, "10.0.0.0/8").unwrap());
    }
}
