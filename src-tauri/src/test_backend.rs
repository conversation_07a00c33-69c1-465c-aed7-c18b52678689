use anyhow::Result;
use std::net::SocketAddr;
use std::sync::Arc;
use async_trait::async_trait;
use log::{info, debug, warn};

use crate::traits::BackendProxy;
use crate::socks5::Socks5Client;

/// Test backend implementation using SOCKS5
pub struct TestSocks5Backend {
    socks5_client: Option<Socks5Client>,
    proxy_address: String,
    username: String,
    password: String,
}

impl TestSocks5Backend {
    pub fn new() -> Self {
        Self {
            socks5_client: None,
            proxy_address: "**************:8888".to_string(),
            username: "lunyduo".to_string(),
            password: "lunyduo888".to_string(),
        }
    }

    /// Initialize SOCKS5 connection
    pub async fn initialize(&mut self) -> Result<()> {
        info!("Initializing SOCKS5 test backend...");
        
        let client = Socks5Client::new(
            &self.proxy_address,
            Some(self.username.clone()),
            Some(self.password.clone()),
        ).await?;

        self.socks5_client = Some(client);
        info!("SOCKS5 test backend initialized successfully");
        Ok(())
    }

    /// Test the SOCKS5 connection
    pub async fn test_connection(&self) -> Result<bool> {
        if let Some(client) = &self.socks5_client {
            info!("Testing SOCKS5 connection...");
            match client.test_http_connectivity().await {
                Ok(success) => {
                    if success {
                        info!("SOCKS5 connection test passed");
                    } else {
                        warn!("SOCKS5 connection test failed");
                    }
                    Ok(success)
                }
                Err(e) => {
                    warn!("SOCKS5 connection test error: {}", e);
                    Ok(false)
                }
            }
        } else {
            warn!("SOCKS5 client not initialized");
            Ok(false)
        }
    }

    /// Send a test HTTP request through SOCKS5
    pub async fn test_http_request(&self, host: &str, path: &str) -> Result<String> {
        if let Some(client) = &self.socks5_client {
            info!("Sending HTTP request to {}:{} through SOCKS5", host, path);
            client.send_http_request(host, path).await
        } else {
            Err(anyhow::anyhow!("SOCKS5 client not initialized"))
        }
    }
}

#[async_trait]
impl BackendProxy for TestSocks5Backend {
    async fn handle_tcp_stream(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
        data: Vec<u8>,
    ) -> Result<Vec<u8>> {
        debug!("Handling TCP stream: {} -> {} ({} bytes)", local_addr, remote_addr, data.len());

        if let Some(client) = &self.socks5_client {
            client.handle_tcp_data(remote_addr, &data).await
        } else {
            warn!("SOCKS5 client not initialized, returning empty response");
            Ok(Vec::new())
        }
    }

    async fn handle_udp_packet(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
        data: Vec<u8>,
    ) -> Result<Vec<u8>> {
        debug!("Handling UDP packet: {} -> {} ({} bytes)", local_addr, remote_addr, data.len());

        if let Some(client) = &self.socks5_client {
            client.handle_udp_data(remote_addr, &data).await
        } else {
            warn!("SOCKS5 client not initialized, returning empty response");
            Ok(Vec::new())
        }
    }

    async fn connect_to_node(&self, node: &crate::traits::ProxyNode) -> Result<()> {
        info!("Connecting to node: {} ({})", node.name, node.address);
        
        // For the test backend, we don't need to do anything special
        // The SOCKS5 client is already initialized
        if self.socks5_client.is_some() {
            info!("Already connected to SOCKS5 proxy");
            Ok(())
        } else {
            Err(anyhow::anyhow!("SOCKS5 client not initialized"))
        }
    }

    async fn disconnect_from_node(&self) -> Result<()> {
        info!("Disconnecting from node");
        // For the test backend, we don't need to do anything special
        Ok(())
    }

    async fn test_node_connectivity(&self, node: &crate::traits::ProxyNode) -> Result<bool> {
        info!("Testing connectivity to node: {}", node.name);
        self.test_connection().await
    }

    async fn get_or_create_tcp_connection(
        &self,
        local_addr: SocketAddr,
        remote_addr: SocketAddr,
    ) -> Result<String> {
        // For test backend, use legacy method
        let connection_id = format!("{}->{}", local_addr, remote_addr);
        debug!("Test backend TCP connection: {}", connection_id);
        Ok(connection_id)
    }

    async fn send_tcp_data(&self, connection_id: &str, data: Vec<u8>) -> Result<()> {
        debug!("Test backend TCP send for {}: {} bytes", connection_id, data.len());
        Ok(())
    }

    async fn receive_tcp_data(&self, connection_id: &str) -> Result<Option<Vec<u8>>> {
        debug!("Test backend TCP receive for {}", connection_id);
        Ok(None)
    }

    async fn close_tcp_connection(&self, connection_id: &str) -> Result<()> {
        debug!("Test backend TCP close for {}", connection_id);
        Ok(())
    }
}

/// Create a test backend instance
pub async fn create_test_backend() -> Result<Arc<TestSocks5Backend>> {
    let mut backend = TestSocks5Backend::new();
    backend.initialize().await?;
    Ok(Arc::new(backend))
}

/// Run connectivity tests
pub async fn run_connectivity_tests(backend: &TestSocks5Backend) -> Result<()> {
    info!("Running SOCKS5 connectivity tests...");

    // Test basic connectivity
    let is_connected = backend.test_connection().await?;
    if !is_connected {
        return Err(anyhow::anyhow!("Basic connectivity test failed"));
    }

    // Test HTTP request
    match backend.test_http_request("httpbin.org", "/ip").await {
        Ok(response) => {
            info!("HTTP test successful, response length: {} bytes", response.len());
            debug!("Response preview: {}", 
                   response.lines().take(3).collect::<Vec<_>>().join("\n"));
        }
        Err(e) => {
            warn!("HTTP test failed: {}", e);
        }
    }

    // Test another endpoint
    match backend.test_http_request("httpbin.org", "/user-agent").await {
        Ok(response) => {
            info!("User-agent test successful, response length: {} bytes", response.len());
        }
        Err(e) => {
            warn!("User-agent test failed: {}", e);
        }
    }

    info!("Connectivity tests completed");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_backend_creation() {
        let backend = TestSocks5Backend::new();
        assert_eq!(backend.proxy_address, "**************:8888");
        assert_eq!(backend.username, "lunyduo");
        assert_eq!(backend.password, "lunyduo888");
    }

    #[tokio::test]
    async fn test_backend_initialization() {
        // This test requires the actual SOCKS5 server to be running
        // Skip in CI/CD environments
        if std::env::var("CI").is_ok() {
            return;
        }

        let mut backend = TestSocks5Backend::new();
        match backend.initialize().await {
            Ok(_) => {
                println!("Backend initialization successful");
                
                // Test connectivity
                match backend.test_connection().await {
                    Ok(true) => println!("Connectivity test passed"),
                    Ok(false) => println!("Connectivity test failed"),
                    Err(e) => println!("Connectivity test error: {}", e),
                }
            }
            Err(e) => {
                println!("Backend initialization failed: {}", e);
                // This is expected if the SOCKS5 server is not available
            }
        }
    }
}
