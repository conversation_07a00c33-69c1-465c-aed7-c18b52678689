{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 3033921117576893, "path": 13554723975383068289, "deps": [[2616743947975331138, "plist", false, 13023799143376813335], [2671782512663819132, "tauri_utils", false, 2542594680736475525], [3060637413840920116, "proc_macro2", false, 16533576608068259436], [3150220818285335163, "url", false, 14925844763445321673], [4899080583175475170, "semver", false, 6282566034919374745], [4974441333307933176, "syn", false, 2247998595589678503], [7170110829644101142, "json_patch", false, 9187213745723820304], [7392050791754369441, "ico", false, 9362011963162037996], [8319709847752024821, "uuid", false, 3024136897767560671], [9556762810601084293, "brotli", false, 13653174948769900030], [9689903380558560274, "serde", false, 745942480493426984], [9857275760291862238, "sha2", false, 2525222478369678447], [10806645703491011684, "thiserror", false, 956444466346166476], [12409575957772518135, "time", false, 14542047284106213166], [12687914511023397207, "png", false, 4796805966879236805], [13077212702700853852, "base64", false, 17365343493898364933], [15367738274754116744, "serde_json", false, 1247084581742840234], [15622660310229662834, "walkdir", false, 16462077771397180365], [17990358020177143287, "quote", false, 14352002541199012783]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-codegen-7af49ca0a524a864/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}