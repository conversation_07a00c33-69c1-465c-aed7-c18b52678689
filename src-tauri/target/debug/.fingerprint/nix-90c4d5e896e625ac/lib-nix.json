{"rustc": 12610991425282158916, "features": "[\"default\", \"ioctl\"]", "declared_features": "[\"acct\", \"aio\", \"default\", \"dir\", \"env\", \"event\", \"fanotify\", \"feature\", \"fs\", \"hostname\", \"inotify\", \"ioctl\", \"kmod\", \"memoffset\", \"mman\", \"mount\", \"mqueue\", \"net\", \"personality\", \"pin-utils\", \"poll\", \"process\", \"pthread\", \"ptrace\", \"quota\", \"reboot\", \"resource\", \"sched\", \"signal\", \"socket\", \"term\", \"time\", \"ucontext\", \"uio\", \"user\", \"zerocopy\"]", "target": 2594889627657062481, "profile": 5347358027863023418, "path": 9485583065506560680, "deps": [[2828590642173593838, "cfg_if", false, 17765236140470246767], [4684437522915235464, "libc", false, 4525917895821538094], [5452785045801004098, "build_script_build", false, 1479139810852255278], [7896293946984509699, "bitflags", false, 1230295567125418219]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/nix-90c4d5e896e625ac/dep-lib-nix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}