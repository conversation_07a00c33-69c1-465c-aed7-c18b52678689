{"rustc": 12610991425282158916, "features": "[\"CKContainer\", \"CKRecord\", \"CKShare\", \"CKShareMetadata\", \"bitflags\"]", "declared_features": "[\"CKAcceptSharesOperation\", \"CKAllowedSharingOptions\", \"CKAsset\", \"CKContainer\", \"CKDatabase\", \"CKDatabaseOperation\", \"CKDefines\", \"CKDiscoverAllUserIdentitiesOperation\", \"CKDiscoverUserIdentitiesOperation\", \"CKError\", \"CKFetchDatabaseChangesOperation\", \"CKFetchNotificationChangesOperation\", \"CKFetchRecordChangesOperation\", \"CKFetchRecordZoneChangesOperation\", \"CKFetchRecordZonesOperation\", \"CKFetchRecordsOperation\", \"CKFetchShareMetadataOperation\", \"CKFetchShareParticipantsOperation\", \"CKFetchSubscriptionsOperation\", \"CKFetchWebAuthTokenOperation\", \"CKLocationSortDescriptor\", \"CKMarkNotificationsReadOperation\", \"CKModifyBadgeOperation\", \"CKModifyRecordZonesOperation\", \"CKModifyRecordsOperation\", \"CKModifySubscriptionsOperation\", \"CKNotification\", \"CKOperation\", \"CKOperationGroup\", \"CKQuery\", \"CKQueryOperation\", \"CKRecord\", \"CKRecordID\", \"CKRecordZone\", \"CKRecordZoneID\", \"CKReference\", \"CKServerChangeToken\", \"CKShare\", \"CKShareMetadata\", \"CKShareParticipant\", \"CKSubscription\", \"CKSyncEngine\", \"CKSyncEngineConfiguration\", \"CKSyncEngineEvent\", \"CKSyncEngineRecordZoneChangeBatch\", \"CKSyncEngineState\", \"CKSystemSharingUIObserver\", \"CKUserIdentity\", \"CKUserIdentityLookupInfo\", \"NSItemProvider_CKSharingSupport\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"objc2-core-location\", \"std\"]", "target": 15282355710264569945, "profile": 8196097686603091492, "path": 232722023961040305, "deps": [[1386409696764982933, "objc2", false, 15586145032784159638], [7896293946984509699, "bitflags", false, 1230295567125418219], [9859211262912517217, "objc2_foundation", false, 27352014241075893]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/objc2-cloud-kit-92b8d256559f3e11/dep-lib-objc2_cloud_kit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}