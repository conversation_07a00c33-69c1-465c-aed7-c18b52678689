{"rustc": 12610991425282158916, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 5347358027863023418, "path": 9504828849963151085, "deps": [[784494742817713399, "tower_service", false, 17026159191486769506], [1906322745568073236, "pin_project_lite", false, 17314440140100030384], [4121350475192885151, "iri_string", false, 14422224486288337314], [5695049318159433696, "tower", false, 6194534012601854814], [7712452662827335977, "tower_layer", false, 3696492881677585687], [7896293946984509699, "bitflags", false, 1230295567125418219], [9010263965687315507, "http", false, 16117512943758922373], [10629569228670356391, "futures_util", false, 12856416791832358000], [14084095096285906100, "http_body", false, 7036155778310679398], [16066129441945555748, "bytes", false, 13648291632396492696]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tower-http-50380f5f7198b448/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}