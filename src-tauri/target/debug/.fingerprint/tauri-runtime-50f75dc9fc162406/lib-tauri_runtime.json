{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 5347358027863023418, "path": 12738171161949863586, "deps": [[2671782512663819132, "tauri_utils", false, 6997172389806444233], [3150220818285335163, "url", false, 6221166370442567446], [4143744114649553716, "raw_window_handle", false, 13961103894552456421], [6089812615193535349, "build_script_build", false, 17170841873206453901], [7606335748176206944, "dpi", false, 1122726338377493003], [9010263965687315507, "http", false, 16117512943758922373], [9689903380558560274, "serde", false, 8729416093781685897], [10806645703491011684, "thiserror", false, 956444466346166476], [15367738274754116744, "serde_json", false, 12334649655770589610], [16727543399706004146, "cookie", false, 1992444425903322060]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-50f75dc9fc162406/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}