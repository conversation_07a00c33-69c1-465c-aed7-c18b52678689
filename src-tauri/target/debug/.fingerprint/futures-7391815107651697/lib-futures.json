{"rustc": 12610991425282158916, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 17669703692130904899, "path": 6242507659744946663, "deps": [[5103565458935487, "futures_io", false, 16751309129792941067], [1811549171721445101, "futures_channel", false, 9941626132382584514], [7013762810557009322, "futures_sink", false, 16019113865635171080], [7620660491849607393, "futures_core", false, 13407759305591013366], [10629569228670356391, "futures_util", false, 12856416791832358000], [12779779637805422465, "futures_executor", false, 3679245604617256597], [16240732885093539806, "futures_task", false, 6033358825451073318]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-7391815107651697/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}