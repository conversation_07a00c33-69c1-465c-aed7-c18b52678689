{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 5347358027863023418, "path": 12460502003051922982, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 13236933696110336055], [3150220818285335163, "url", false, 6221166370442567446], [3191507132440681679, "serde_untagged", false, 11361284864734085947], [4071963112282141418, "serde_with", false, 2054338885233154351], [4899080583175475170, "semver", false, 6822394956566614587], [5986029879202738730, "log", false, 11404600273113955178], [6606131838865521726, "ctor", false, 6702809386760566390], [7170110829644101142, "json_patch", false, 1240217841667434409], [8319709847752024821, "uuid", false, 2195303533259590807], [9010263965687315507, "http", false, 16117512943758922373], [9451456094439810778, "regex", false, 6253501835833883984], [9556762810601084293, "brotli", false, 13653174948769900030], [9689903380558560274, "serde", false, 8729416093781685897], [10806645703491011684, "thiserror", false, 956444466346166476], [11989259058781683633, "dunce", false, 13468389862681908181], [13625485746686963219, "anyhow", false, 16973122078729232300], [15367738274754116744, "serde_json", false, 12334649655770589610], [15609422047640926750, "toml", false, 17281185775222264479], [15622660310229662834, "walkdir", false, 16462077771397180365], [15932120279885307830, "memchr", false, 18381834597950626254], [17146114186171651583, "infer", false, 15032710560163361997], [17155886227862585100, "glob", false, 1180651903813024181], [17186037756130803222, "phf", false, 2108580828916697825]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-94a7da40704edb84/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}