{"rustc": 12610991425282158916, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 3033921117576893, "path": 6981224517068874896, "deps": [[2671782512663819132, "tauri_utils", false, 2542594680736475525], [4899080583175475170, "semver", false, 6282566034919374745], [6913375703034175521, "schemars", false, 17576016863933316537], [7170110829644101142, "json_patch", false, 9187213745723820304], [9689903380558560274, "serde", false, 745942480493426984], [12714016054753183456, "tauri_winres", false, 1125219758871886651], [13077543566650298139, "heck", false, 10538095813257807281], [13475171727366188400, "cargo_toml", false, 16514883598790867137], [13625485746686963219, "anyhow", false, 16973122078729232300], [15367738274754116744, "serde_json", false, 1247084581742840234], [15609422047640926750, "toml", false, 1007043222167268383], [15622660310229662834, "walkdir", false, 16462077771397180365], [16928111194414003569, "dirs", false, 4876374535699893364], [17155886227862585100, "glob", false, 1180651903813024181]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-build-1d113c2e77c04578/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}