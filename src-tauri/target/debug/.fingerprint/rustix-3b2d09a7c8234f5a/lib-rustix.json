{"rustc": 12610991425282158916, "features": "[\"alloc\", \"fs\", \"libc-extra-traits\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 11375657722578241393, "path": 11728609263139049535, "deps": [[3430646239657634944, "build_script_build", false, 10218756938058892231], [4684437522915235464, "libc", false, 4525917895821538094], [7896293946984509699, "bitflags", false, 1230295567125418219], [8253628577145923712, "libc_errno", false, 17629163392511475100]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-3b2d09a7c8234f5a/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}