{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 17729593404256868254], [8324462083842905811, "build_script_build", false, 5763299253119894067], [10075813589556262423, "build_script_build", false, 8206354781236613206]], "local": [{"RerunIfChanged": {"output": "debug/build/tun-proxy-ui-1ea96ac6df498065/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": "{\"build\":{\"devUrl\":\"http://127.0.0.1:1430\"}}"}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}