/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/deps/app_lib.d: src/lib.rs src/traits.rs src/tun.rs src/packet.rs src/dns.rs src/rules.rs src/config.rs src/proxy.rs src/socks5.rs src/route.rs src/test_backend.rs src/stats.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/a6bae871e3b336e8befd9c050cca8ed92de3c858f0b909158188b10d278aec65 /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5 /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/c00117533225ca5a959bd1c8d3760e285f6caefb76214c5f7263638d2ddca70c

/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/deps/libapp_lib.a: src/lib.rs src/traits.rs src/tun.rs src/packet.rs src/dns.rs src/rules.rs src/config.rs src/proxy.rs src/socks5.rs src/route.rs src/test_backend.rs src/stats.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/a6bae871e3b336e8befd9c050cca8ed92de3c858f0b909158188b10d278aec65 /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5 /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/c00117533225ca5a959bd1c8d3760e285f6caefb76214c5f7263638d2ddca70c

/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/deps/libapp_lib.dylib: src/lib.rs src/traits.rs src/tun.rs src/packet.rs src/dns.rs src/rules.rs src/config.rs src/proxy.rs src/socks5.rs src/route.rs src/test_backend.rs src/stats.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/a6bae871e3b336e8befd9c050cca8ed92de3c858f0b909158188b10d278aec65 /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5 /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/c00117533225ca5a959bd1c8d3760e285f6caefb76214c5f7263638d2ddca70c

/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/deps/libapp_lib.rlib: src/lib.rs src/traits.rs src/tun.rs src/packet.rs src/dns.rs src/rules.rs src/config.rs src/proxy.rs src/socks5.rs src/route.rs src/test_backend.rs src/stats.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/a6bae871e3b336e8befd9c050cca8ed92de3c858f0b909158188b10d278aec65 /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5 /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/c00117533225ca5a959bd1c8d3760e285f6caefb76214c5f7263638d2ddca70c

src/lib.rs:
src/traits.rs:
src/tun.rs:
src/packet.rs:
src/dns.rs:
src/rules.rs:
src/config.rs:
src/proxy.rs:
src/socks5.rs:
src/route.rs:
src/test_backend.rs:
src/stats.rs:
/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/a6bae871e3b336e8befd9c050cca8ed92de3c858f0b909158188b10d278aec65:
/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5:
/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/c00117533225ca5a959bd1c8d3760e285f6caefb76214c5f7263638d2ddca70c:

# env-dep:CARGO_PKG_AUTHORS=lulin
# env-dep:CARGO_PKG_DESCRIPTION=Cross-platform proxy tool with TUN/TAP support
# env-dep:CARGO_PKG_NAME=tun-proxy-ui
# env-dep:OUT_DIR=/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out
