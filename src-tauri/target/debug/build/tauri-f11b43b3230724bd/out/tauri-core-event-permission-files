["/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-f11b43b3230724bd/out/permissions/event/autogenerated/commands/emit.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-f11b43b3230724bd/out/permissions/event/autogenerated/commands/emit_to.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-f11b43b3230724bd/out/permissions/event/autogenerated/commands/listen.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-f11b43b3230724bd/out/permissions/event/autogenerated/commands/unlisten.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-f11b43b3230724bd/out/permissions/event/autogenerated/default.toml"]